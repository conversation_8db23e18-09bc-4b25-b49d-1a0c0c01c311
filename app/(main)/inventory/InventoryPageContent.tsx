'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/app/context/ThemeContext';
import { useAppContext } from '@/app/context/AppContext';
import { Button } from '@/app/components/forms/Button';
import type { PartFormData } from '@/app/components/forms/PartForm';
import { PartForm } from '@/app/components/forms/PartForm';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { createInventoryColumns, InventoryColumnData, InventoryTableActions } from '@/app/components/data-display/data-table/column-definitions';
import { Plus, RefreshCw, SlidersHorizontal } from 'lucide-react';
import { showErrorToast, showSuccessToast } from '@/app/components/feedback';
import { Badge } from '@/app/components/data-display/badge';

import {
    DEFAULT_FILTER_STATE,
    FilterState,
    InventoryFilter,
    applyFilters,
    loadFiltersFromStorage,
    saveFiltersToStorage
} from '@/app/components/inventory/filters';
import { debounce } from '@/app/lib/utils';
import { Product } from '@/app/types/inventory';

// Define InventoryItem interface matching what InventoryTable expects
interface InventoryItem {
  _id: string;
  id: string;
  partNumber: string;
  name: string;
  businessName?: string | null;
  description: string;
  currentStock?: number; // Add currentStock at top level
  inventory: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    currentStock?: number;
    warehouseId?: { _id: string; name: string; location?: string } | string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
  supplierId?: { _id: string; name: string } | string;
  unitOfMeasure?: string;
  costPrice?: number;
  cost?: number;
  categoryId?: { _id: string; name: string } | string;
  reorderLevel?: number;
  status?: string;
  isManufactured?: boolean;
  technicalSpecs?: string;
  isAssembly?: boolean;
  schemaVersion?: number;
  subParts?: Array<{ partId: string; quantity: number }>;
  createdAt?: string;
  updatedAt?: string;
}

// Function to map API product data to InventoryItem format
const mapToAppProduct = (apiProduct: any): InventoryItem | null => {
  if (!apiProduct || !apiProduct._id) {
    console.warn('[mapToAppProduct] Invalid product data:', apiProduct);
    return null;
  }

  return {
    _id: apiProduct._id,
    id: apiProduct._id,
    name: apiProduct.name || 'Unnamed Part',
    businessName: apiProduct.businessName || null,
    description: apiProduct.description || '',
    technicalSpecs: apiProduct.technicalSpecs || '',
    isManufactured: apiProduct.isManufactured || false,
    status: apiProduct.status || 'active',
    supplierId: apiProduct.supplierId || apiProduct.supplier || null,
    unitOfMeasure: apiProduct.unitOfMeasure || 'pcs',
    costPrice: apiProduct.costPrice || apiProduct.cost || 0,
    cost: apiProduct.cost || apiProduct.costPrice || 0,
    categoryId: apiProduct.categoryId || null,
    reorderLevel: apiProduct.reorderLevel || 0,
    isAssembly: apiProduct.isAssembly || false,
    schemaVersion: apiProduct.schemaVersion || 1,
    subParts: apiProduct.subParts || [],
    createdAt: apiProduct.createdAt || '',
    updatedAt: apiProduct.updatedAt || '',
    inventory: {
      stockLevels: apiProduct.inventory?.stockLevels || {
        raw: 0,
        hardening: 0,
        grinding: 0,
        finished: apiProduct.inventory?.currentStock || apiProduct.currentStock || 0,
        rejected: 0
      },
      currentStock: apiProduct.inventory?.currentStock || apiProduct.currentStock || 0,
      warehouseId: apiProduct.inventory?.warehouseId || apiProduct.warehouseId || null,
      safetyStockLevel: apiProduct.inventory?.safetyStockLevel || 0,
      maximumStockLevel: apiProduct.inventory?.maximumStockLevel || 0,
      averageDailyUsage: apiProduct.inventory?.averageDailyUsage || 0,
      abcClassification: apiProduct.inventory?.abcClassification || 'C',
      lastStockUpdate: apiProduct.inventory?.lastStockUpdate ? new Date(apiProduct.inventory.lastStockUpdate) : null
    },
    // Use finished stock as primary stock count (replaces legacy currentStock)
    currentStock: Number(apiProduct.inventory?.stockLevels?.finished ?? apiProduct.currentStock ?? 0),
    // Ensure partNumber is present, falling back to productCode or an empty string
    partNumber: apiProduct.partNumber || apiProduct.productCode || '',
  };
};

export function InventoryPageContent() {
  const {
    products: srcProducts,
    isLoading,
    isUsingMockData,
    addProduct,
    updateProduct,
    deleteProduct,
    getProducts,
  } = useAppContext();

  // Performance: Use refs for values that don't need to trigger re-renders
  const abortControllerRef = useRef<AbortController | null>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // State for showing/hiding the filter panel and modal
  const [showFilters, setShowFilters] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedPart, setSelectedPart] = useState<InventoryItem | null>(null);

  // Advanced filtering state
  const [filterState, setFilterState] = useState<FilterState>(DEFAULT_FILTER_STATE);
  const [searchQuery, setSearchQuery] = useState('');

  // State for filter dropdown data
  const [suppliers, setSuppliers] = useState<string[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [locations, setLocations] = useState<string[]>([]);
  const [filterDataLoading, setFilterDataLoading] = useState(false);
  const [filterDataError, setFilterDataError] = useState<string | null>(null);

  // Server-side pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [productsPerPage, setProductsPerPage] = useState(50);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [serverParts, setServerParts] = useState<any[]>([]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Load filters from localStorage on mount
  useEffect(() => {
    const savedFilters = loadFiltersFromStorage();
    if (savedFilters) {
      setFilterState(savedFilters);
    }
  }, []);

  // Save filters to localStorage when they change
  useEffect(() => {
    saveFiltersToStorage(filterState);
  }, [filterState]);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        const result = await getProducts({ page: 1, limit: productsPerPage });
        setTotalItems(result.pagination.total);
        setTotalPages(result.pagination.pages);
        setCurrentPage(result.pagination.page);
      } catch (error) {
        console.error('[DEBUG] Error initializing data:', error);
      }
    };

    initializeData();
  }, [getProducts, productsPerPage]);

  // Fetch filter dropdown data on mount
  useEffect(() => {
    const fetchFilterData = async () => {
      setFilterDataLoading(true);
      setFilterDataError(null);

      try {
        // Fetch suppliers, categories, and locations in parallel
        const [suppliersResponse, categoriesResponse, locationsResponse] = await Promise.all([
          fetch('/api/suppliers?limit=1000').then(res => res.json()),
          fetch('/api/categories?limit=1000').then(res => res.json()),
          fetch('/api/locations?limit=1000').then(res => res.json())
        ]);

        // Process suppliers data
        if (suppliersResponse.data && Array.isArray(suppliersResponse.data)) {
          const supplierNames = suppliersResponse.data.map((supplier: any) => supplier.name).filter(Boolean);
          setSuppliers(supplierNames);
          console.log(`[FRONTEND DEBUG] Loaded ${supplierNames.length} suppliers for filters`);
        }

        // Process categories data
        if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
          const categoryData = categoriesResponse.data.map((category: any) => ({
            id: category._id || category.id,
            name: category.name
          })).filter((cat: { id: string; name: string }) => cat.id && cat.name);
          setCategories(categoryData);
          console.log(`[FRONTEND DEBUG] Loaded ${categoryData.length} categories for filters`);
        }

        // Process locations data
        if (locationsResponse.data && Array.isArray(locationsResponse.data)) {
          const locationNames = locationsResponse.data.map((location: any) => location.name).filter(Boolean);
          setLocations(locationNames);
          console.log(`[FRONTEND DEBUG] Loaded ${locationNames.length} locations for filters`);
        }

      } catch (error) {
        console.error('[FRONTEND DEBUG] Error fetching filter data:', error);
        setFilterDataError('Failed to load filter options');
      } finally {
        setFilterDataLoading(false);
      }
    };

    fetchFilterData();
  }, []);

  // Apply filters to products (client-side filtering)
  const filteredProducts = useMemo(() => {
    // If a search query is active, the data comes from serverParts
    if (searchQuery && serverParts && serverParts.length > 0) {
      return serverParts.map(mapToAppProduct).filter(p => p !== null) as InventoryItem[];
    }
    if (searchQuery) {
        return [];
    }

    // Use AppContext products (already processed with correct currentStock)
    if (srcProducts && srcProducts.length > 0) {
      return applyFilters(srcProducts as unknown as Product[], filterState) as unknown as InventoryItem[];
    }

    return [];
  }, [srcProducts, filterState, searchQuery, serverParts]);

  // Performance: Memoize the display data to avoid unnecessary recalculations
  const displayProducts = useMemo(() => filteredProducts, [filteredProducts]);
  const displayTotalItems = useMemo(() => totalItems, [totalItems]);

  // Performance: Optimized debounced server search function with abort controller
  const debouncedServerSearch = useCallback(
    debounce(async (query: string, page: number = 1, pageSize: number = productsPerPage) => {
      if (!query.trim()) {
        setServerParts([]);
        setTotalItems(0);
        setTotalPages(1);
        return;
      }

      // Cancel previous request if still pending
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      try {
        console.log(`[FRONTEND DEBUG] Searching for: "${query}" (page ${page}, pageSize ${pageSize})`);

        const response = await fetch(`/api/parts/search?q=${encodeURIComponent(query)}&page=${page}&limit=${pageSize}`, {
          signal: abortControllerRef.current.signal
        });

        if (!response.ok) {
          throw new Error(`Search failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Check if component is still mounted before updating state
        if (!isMountedRef.current) return;

        console.log('[FRONTEND DEBUG] Search result:', result);

        // Handle both success/data format and direct data format
        if (result.success && result.data) {
          setServerParts(result.data.parts || result.data || []);
          setTotalItems(result.data.pagination?.total || result.pagination?.total || 0);
          setTotalPages(result.data.pagination?.pages || result.pagination?.pages || 1);
          setCurrentPage(result.data.pagination?.page || result.pagination?.page || 1);
        } else if (result.data && Array.isArray(result.data)) {
          // Handle direct data array format
          setServerParts(result.data);
          setTotalItems(result.pagination?.total || result.data.length);
          setTotalPages(result.pagination?.pages || Math.ceil(result.data.length / pageSize));
          setCurrentPage(result.pagination?.page || 1);
        } else {
          console.error('[FRONTEND DEBUG] Search API returned unsuccessful result:', result);
          setServerParts([]);
          setTotalItems(0);
          setTotalPages(1);
        }
      } catch (error) {
        console.error('[FRONTEND DEBUG] Search error:', error);
        setServerParts([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    }, 300),
    [productsPerPage]
  );

  // Removed conflicting useEffect - pagination is handled by handlePageChange function

  // Fetch server parts function
  const fetchServerParts = useCallback(async (page: number = 1, query: string = searchQuery, pageSize: number = productsPerPage) => {
    if (!query.trim()) {
      setServerParts([]);
      setTotalItems(0);
      setTotalPages(1);
      return;
    }

    try {
      console.log(`[FRONTEND DEBUG] Fetching server parts: "${query}" (page ${page}, pageSize ${pageSize})`);

      const response = await fetch(`/api/parts/search?q=${encodeURIComponent(query)}&page=${page}&limit=${pageSize}`);

      if (!response.ok) {
        throw new Error(`Fetch failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('[FRONTEND DEBUG] Fetch result:', result);

      // Handle both success/data format and direct data format
      if (result.success && result.data) {
        setServerParts(result.data.parts || result.data || []);
        setTotalItems(result.data.pagination?.total || result.pagination?.total || 0);
        setTotalPages(result.data.pagination?.pages || result.pagination?.pages || 1);
        setCurrentPage(result.data.pagination?.page || result.pagination?.page || 1);
      } else if (result.data && Array.isArray(result.data)) {
        // Handle direct data array format
        setServerParts(result.data);
        setTotalItems(result.pagination?.total || result.data.length);
        setTotalPages(result.pagination?.pages || Math.ceil(result.data.length / pageSize));
        setCurrentPage(result.pagination?.page || 1);
      } else {
        console.error('[FRONTEND DEBUG] Fetch API returned unsuccessful result:', result);
        setServerParts([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Fetch error:', error);
      setServerParts([]);
      setTotalItems(0);
      setTotalPages(1);
    }
  }, [searchQuery, productsPerPage]);

  // Handle search change
  const handleSearchChange = (term: string) => {
    setSearchQuery(term);
    setCurrentPage(1);

    if (term.trim()) {
      debouncedServerSearch(term, 1, productsPerPage);
    } else {
      // Clear server search results and return to normal pagination
      setServerParts([]);
      setTotalItems(0);
      setTotalPages(1);

      // Fetch first page of normal data
      getProducts({ page: 1, limit: productsPerPage })
        .then(result => {
          setTotalItems(result.pagination.total);
          setTotalPages(result.pagination.pages);
          setCurrentPage(result.pagination.page);
        })
        .catch(error => {
          console.error('[DEBUG] Error fetching normal data after search clear:', error);
        });
    }
  };

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    console.log(`[FRONTEND DEBUG] handlePageChange called with pageNumber: ${pageNumber}`);
    setCurrentPage(pageNumber);
    if (searchQuery) {
      console.log(`[FRONTEND DEBUG] Using search API for page ${pageNumber} with query: "${searchQuery}"`);
      fetchServerParts(pageNumber, searchQuery);
    } else {
      console.log(`[FRONTEND DEBUG] Using regular API for page ${pageNumber}`);
      getProducts({ page: pageNumber, limit: productsPerPage })
        .then(result => {
          console.log(`[FRONTEND DEBUG] Page change completed, pagination:`, result.pagination);
          console.log(`[FRONTEND DEBUG] Received ${result.products?.length || 0} products for page ${pageNumber}`);
          setTotalItems(result.pagination.total);
          setTotalPages(result.pagination.pages);
          setCurrentPage(result.pagination.page);
          setServerParts(result.products || []);
        })
        .catch(error => {
          console.error(`[FRONTEND DEBUG] Error in page change:`, error);
        });
    }
  };

  // Handle pagination change from StandardizedTable
  const handlePaginationChange = (pagination: { pageIndex: number; pageSize: number }) => {
    console.log(`[FRONTEND DEBUG] handlePaginationChange called with:`, pagination);
    const newPage = pagination.pageIndex + 1;
    const newPageSize = pagination.pageSize;
    console.log(`[FRONTEND DEBUG] Calculated newPage: ${newPage}, newPageSize: ${newPageSize}`);
    console.log(`[FRONTEND DEBUG] Current state - currentPage: ${currentPage}, productsPerPage: ${productsPerPage}`);

    if (newPageSize !== productsPerPage) {
      console.log(`[FRONTEND DEBUG] Page size changed from ${productsPerPage} to ${newPageSize}`);
      setProductsPerPage(newPageSize);
      setCurrentPage(1);
      if (searchQuery) {
        fetchServerParts(1, searchQuery, newPageSize);
      } else {
        getProducts({ page: 1, limit: newPageSize })
          .then(result => {
            setTotalItems(result.pagination.total);
            setTotalPages(result.pagination.pages);
            setCurrentPage(result.pagination.page);
            setServerParts(result.products || []);
          })
          .catch(error => {
            console.error('[DEBUG] Error in page size change:', error);
          });
      }
    } else if (newPage !== currentPage) {
      console.log(`[FRONTEND DEBUG] Page changed from ${currentPage} to ${newPage}, calling handlePageChange`);
      handlePageChange(newPage);
    } else {
      console.log(`[FRONTEND DEBUG] No changes detected - newPage: ${newPage}, currentPage: ${currentPage}, newPageSize: ${newPageSize}, productsPerPage: ${productsPerPage}`);
    }
  };

  // Performance: Memoize the data transformation function
  const adaptProductsForTable = useCallback((products: InventoryItem[]): InventoryColumnData[] => {
    return products.map((product) => {
      const finishedStock = product.inventory?.stockLevels?.finished ?? product.inventory?.currentStock ?? 0;

      return {
        _id: product._id,
        id: product.id || product._id,
        partNumber: product.partNumber,
        name: product.name,
        businessName: product.businessName ?? null,
        description: product.description,
        unitOfMeasure: product.unitOfMeasure || 'pcs',
        currentStock: finishedStock,
        reorderLevel: product.reorderLevel || 0,
        supplier: typeof product.supplierId === 'object' && product.supplierId !== null
          ? { _id: product.supplierId._id, name: product.supplierId.name }
          : { _id: '', name: '' },
        inventory: {
          stockLevels: product.inventory?.stockLevels || {
            raw: 0,
            hardening: 0,
            grinding: 0,
            finished: finishedStock,
            rejected: 0
          },
          currentStock: finishedStock,
          warehouseId: typeof product.inventory?.warehouseId === 'string'
            ? product.inventory.warehouseId
            : product.inventory?.warehouseId?._id || '',
          safetyStockLevel: product.inventory?.safetyStockLevel || 0,
          maximumStockLevel: product.inventory?.maximumStockLevel || 0,
          averageDailyUsage: product.inventory?.averageDailyUsage || 0,
          abcClassification: product.inventory?.abcClassification || 'C',
          lastStockUpdate: product.inventory?.lastStockUpdate || null
        },
        stockLevels: product.inventory?.stockLevels || {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: finishedStock,
          rejected: 0
        },
        createdAt: product.createdAt || '',
        updatedAt: product.updatedAt || ''
      };
    });
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  // Performance: Memoize the transformed table data
  const tableData = useMemo(() => {
    return adaptProductsForTable(displayProducts);
  }, [displayProducts, adaptProductsForTable]);

  // Refresh function for actions
  const handleRefreshData = async () => {
    try {
      console.log(`[FRONTEND DEBUG] Refreshing data after part action`);

      if (searchQuery) {
        await fetchServerParts(currentPage, searchQuery);
      } else {
        const result = await getProducts({ page: currentPage, limit: productsPerPage });
        setTotalItems(result.pagination.total);
        setTotalPages(result.pagination.pages);
        setCurrentPage(result.pagination.page);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error refreshing data:', error);
    }
  };

  // Handle add part
  const handleAddPart = async (formData: PartFormData) => {
    try {
      console.log('[FRONTEND DEBUG] Adding part:', formData);
      // Ensure partNumber is provided and fix type issues
      const dataWithPartNumber: any = {
        ...formData,
        partNumber: formData.partNumber || `P${Date.now().toString().slice(-6)}`,
        businessName: formData.businessName ?? null
      };

      // Only include categoryId and supplierId if they have values
      if (formData.categoryId) {
        dataWithPartNumber.categoryId = formData.categoryId;
      }
      if (formData.supplierId) {
        dataWithPartNumber.supplierId = formData.supplierId;
      }
      await addProduct(dataWithPartNumber);
      setShowAddForm(false);
      showSuccessToast('Part added successfully');
      await handleRefreshData();
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error adding part:', error);
      showErrorToast({ error: 'Failed to add part' });
    }
  };

  // Handle edit part
  const handleEditPart = async (formData: PartFormData) => {
    try {
      console.log('[FRONTEND DEBUG] Editing part:', formData);
      if (!formData._id) {
        throw new Error('Part ID is required for editing');
      }
      // Ensure partNumber is provided and fix type issues
      const dataWithPartNumber: any = {
        ...formData,
        partNumber: formData.partNumber || `P${Date.now().toString().slice(-6)}`,
        businessName: formData.businessName ?? null
      };

      // Only include categoryId and supplierId if they have values
      if (formData.categoryId) {
        dataWithPartNumber.categoryId = formData.categoryId;
      }
      if (formData.supplierId) {
        dataWithPartNumber.supplierId = formData.supplierId;
      }
      await updateProduct(formData._id, dataWithPartNumber);
      setShowEditForm(false);
      setSelectedPart(null);
      showSuccessToast('Part updated successfully');
      await handleRefreshData();
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error editing part:', error);
      showErrorToast({ error: 'Failed to update part' });
    }
  };

  // Handle delete part
  const handleDeletePart = async (partId: string) => {
    try {
      console.log('[FRONTEND DEBUG] Deleting part:', partId);
      await deleteProduct(partId);
      showSuccessToast('Part deleted successfully');
      await handleRefreshData();
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error deleting part:', error);
      showErrorToast({ error: 'Failed to delete part' });
    }
  };

  // Create table actions
  const tableActions: InventoryTableActions = useMemo(() => ({
    onView: (item) => {
      console.log('View item:', item);
      // Could navigate to detail page or open modal
    },
    onEdit: (item) => {
      setSelectedPart(item as any);
      setShowEditForm(true);
    },
    onDelete: (item) => handleDeletePart(item._id),
    onRefresh: handleRefreshData
  }), []);

  // Create table columns
  const columns = useMemo(() => createInventoryColumns(tableActions), [tableActions]);

  // Check if filters are active
  const hasActiveFilters = useMemo(() => {
    return filterState.stockQuantity.enabled ||
           filterState.reorderLevel.enabled ||
           filterState.supplier.enabled ||
           filterState.category.enabled ||
           filterState.location.enabled;
  }, [filterState]);

  // Render filters function for StandardizedTable
  const renderFilters = () => (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        onClick={() => setShowFilters(!showFilters)}
        className="flex items-center gap-2"
      >
        <SlidersHorizontal size={16} />
        Filters
        {hasActiveFilters && <Badge variant="secondary" className="ml-1">Active</Badge>}
      </Button>
      <Button
        variant="outline"
        onClick={() => {
          if (searchQuery) {
            fetchServerParts(currentPage, searchQuery);
          } else {
            getProducts({ page: currentPage, limit: productsPerPage })
              .then(result => {
                console.log('[DEBUG] Refresh completed, pagination:', result.pagination);
                setTotalItems(result.pagination.total);
                setTotalPages(result.pagination.pages);
                setCurrentPage(result.pagination.page);
              })
              .catch(error => {
                console.error('[DEBUG] Error in refresh:', error);
              });
          }
        }}
        className="p-2"
        title="Refresh data"
      >
        <RefreshCw size={16} />
      </Button>
    </div>
  );

  // Render actions function for StandardizedTable
  const renderActions = () => (
    <Button
      onClick={() => setShowAddForm(true)}
      className="flex items-center gap-2"
    >
      <Plus size={16} />
      Add Part
    </Button>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Filter Panel */}
      {showFilters && (
        <div className="mb-6">
          <InventoryFilter
            filters={filterState}
            onFiltersChange={setFilterState}
            onReset={() => setFilterState(DEFAULT_FILTER_STATE)}
            suppliers={suppliers}
            categories={categories}
            locations={locations}
          />
        </div>
      )}

      {/* StandardizedTable */}
      <StandardizedTable
        data={tableData}
        columns={columns}
        searchPlaceholder="Search parts by name, part number, or description..."
        enableViewToggle={false}
        enableSearch={true}
        onSearchChange={handleSearchChange}
        renderFilters={renderFilters}
        renderActions={renderActions}
        tableProps={{
          manualPagination: true,
          totalRows: displayTotalItems,
          onPaginationChange: handlePaginationChange,
          initialPagination: { pageIndex: currentPage - 1, pageSize: productsPerPage },
          pageSizeOptions: [10, 20, 50, 100, 150],
          isLoading: isLoading,
        }}
      />

      {/* Add Part Modal */}
      {showAddForm && (
        <PartForm
          onSubmit={handleAddPart}
          onClose={() => setShowAddForm(false)}
        />
      )}

      {/* Edit Part Modal */}
      {showEditForm && selectedPart && (
        <PartForm
          initialData={{
            _id: selectedPart.id,
            name: selectedPart.name,
            businessName: selectedPart.businessName || null,
            description: selectedPart.description || '',
            technicalSpecs: selectedPart.technicalSpecs || '',
            isManufactured: selectedPart.isManufactured || false,
            partNumber: selectedPart.partNumber || '',
            reorderLevel: selectedPart.reorderLevel || 0,
            status: (selectedPart.status as 'active' | 'inactive' | 'obsolete') || 'active',
            inventory: {
              stockLevels: selectedPart.inventory?.stockLevels || {
                raw: 0,
                hardening: 0,
                grinding: 0,
                finished: selectedPart.inventory?.currentStock || 0,
                rejected: 0
              },
              lastStockUpdate: selectedPart.inventory?.lastStockUpdate ? new Date(selectedPart.inventory.lastStockUpdate) : null,
              warehouseId: (typeof selectedPart.inventory?.warehouseId === 'object' && selectedPart.inventory?.warehouseId !== null ? selectedPart.inventory.warehouseId._id : selectedPart.inventory?.warehouseId) || '',
              safetyStockLevel: selectedPart.inventory?.safetyStockLevel || 0,
              maximumStockLevel: selectedPart.inventory?.maximumStockLevel || 0,
              averageDailyUsage: selectedPart.inventory?.averageDailyUsage || 0,
              abcClassification: (selectedPart.inventory?.abcClassification || 'C') as 'A' | 'B' | 'C'
            },
            supplierId: (typeof selectedPart.supplierId === 'object' && selectedPart.supplierId !== null ? selectedPart.supplierId._id : selectedPart.supplierId) || '',
            unitOfMeasure: selectedPart.unitOfMeasure || 'pcs',
            costPrice: selectedPart.costPrice || selectedPart.cost || 0,
            categoryId: typeof selectedPart.categoryId === 'object' && selectedPart.categoryId !== null
              ? selectedPart.categoryId._id
              : (selectedPart.categoryId || ''),
            isAssembly: selectedPart.isAssembly || false,
            schemaVersion: selectedPart.schemaVersion || 1,
            subParts: selectedPart.subParts
              ? selectedPart.subParts.map(part => ({
                  partId: part.partId || '',
                  quantity: part.quantity
                }))
              : []
          }}
          onSubmit={handleEditPart}
          onClose={() => {
            setShowEditForm(false);
            setSelectedPart(null);
          }}
          isEdit={true}
          title="Edit Part"
        />
      )}
    </div>
  );
}
