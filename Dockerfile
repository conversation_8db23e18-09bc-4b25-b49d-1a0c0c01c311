# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use a Node.js image
FROM node:18-alpine AS builder

# Install pnpm
RUN npm install -g pnpm

# Set the working directory
WORKDIR /app

# Copy package.json and pnpm-lock.yaml to leverage Docker cache for dependencies
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Copy the source code
COPY src ./src
COPY tsconfig.json ./

# Build the project
RUN pnpm build

# Use a smaller Node.js image for running the application
FROM node:18-alpine

# Set the working directory
WORKDIR /app

# Copy the built files and package.json
COPY --from=builder /app/build ./build
COPY --from=builder /app/package.json ./

# Install only production dependencies
RUN pnpm install --prod

# Set the entry point for the application
ENTRYPOINT ["node", "build/index.js"]
