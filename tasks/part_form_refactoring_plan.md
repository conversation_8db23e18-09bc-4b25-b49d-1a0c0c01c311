# PartForm Refactoring Plan

## Overview
This document outlines the plan for refactoring the PartForm component from a single client component into separate client and server components following Next.js best practices.

## Current Implementation Analysis

The current `PartForm.tsx` is a client component that:
- Has the `"use client"` directive at the top
- Uses React hooks (useState, useForm)
- Handles form validation with Zod and react-hook-form
- Manages form submission state
- Renders a complex form with multiple input fields
- Uses controllers from react-hook-form

## Refactoring Strategy

### 1. Directory Structure
Create a proper directory structure:
```
app/components/forms/PartForm/
├── PartForm.tsx (server component)
├── PartFormClient.tsx (client component)
├── types.ts (shared types)
├── index.ts (export file)
```

### 2. Types Migration
Move the interfaces and types to a separate `types.ts` file:
- `Database` interface
- `Part` type
- `PartInventory` type
- `PartFormData` type
- `PartFormProps` interface
- Zod schema

### 3. Component Separation

#### Server Component (PartForm.tsx)
- Remove "use client" directive
- Create a wrapper component that accepts the props and passes them to the client component
- Simple rendering logic only

#### Client Component (PartFormClient.tsx)
- Add "use client" directive
- Keep all interactive logic (form handling, validation, state management)
- Keep form rendering and JSX
- Import types from types.ts

### 4. Implementation Steps

1. **Create types.ts**
   - Extract interfaces and types from the current component
   - Include Zod schema

2. **Create PartFormClient.tsx**
   - Copy most of the current implementation
   - Import types from types.ts
   - Keep all interactive logic
   - Ensure it has the "use client" directive

3. **Update PartForm.tsx**
   - Remove "use client" directive
   - Create a simple component that forwards props to the client component
   - Import the client component and types

4. **Create index.ts**
   - Export the PartForm component
   - Export types needed by consumers

5. **Test Functionality**
   - Ensure the refactored component behaves the same as the original
   - Test form validation
   - Test form submission

## Code Examples

### types.ts
```typescript
import * as z from 'zod';

// Database type definitions
export interface Database {
  public: {
    Tables: {
      parts: {
        Insert: {
          part_id: string;
          name: string;
          description?: string | null;
          technical_specs?: string | null;
          is_external?: boolean;
          supplier_id?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      inventory: {
        Insert: {
          part_id: string;
          current_stock: number;
          reorder_level: number;
          location?: string | null;
        };
      };
    };
  };
}

export type Part = Database['public']['Tables']['parts']['Insert'];
export type PartInventory = Database['public']['Tables']['inventory']['Insert'];

// Zod validation schema
export const partFormSchema = z.object({
  part_id: z.string().min(3, { message: "Part ID must be at least 3 characters" }),
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  description: z.string().optional(),
  technical_specs: z.string().optional(),
  is_external: z.boolean(),
  supplier_id: z.number().nullable().optional(),
  // Inventory fields
  current_stock: z.number().min(0, { message: "Stock cannot be negative" }),
  reorder_level: z.number().min(0, { message: "Reorder level cannot be negative" }),
  safety_stock_level: z.number().min(0, { message: "Safety stock level cannot be negative" }).optional(),
  maximum_stock_level: z.number().min(0, { message: "Maximum stock level cannot be negative" }).optional(),
  location: z.string().optional()
});

export type PartFormData = z.infer<typeof partFormSchema>;

/**
 * Props for the PartForm component
 */
export interface PartFormProps {
  /** Function to handle form submission */
  onSubmit: (data: PartFormData) => Promise<void>;
  /** Function to close the form */
  onClose: () => void;
  /** Initial data for editing an existing part */
  initialData?: Partial<PartFormData>;
  /** Whether the form is in edit mode */
  isEdit?: boolean;
  /** Custom title for the form */
  title?: string;
}
```

### PartForm.tsx (Server Component)
```typescript
import { PartFormProps } from './types';
import PartFormClient from './PartFormClient';

/**
 * Server component wrapper for PartForm
 * Delegates rendering to the client component
 */
export function PartForm(props: PartFormProps) {
  return <PartFormClient {...props} />;
}
```

### PartFormClient.tsx (Client Component)
```typescript
"use client";

import React, { useState } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormContainer } from '@/app/components/ui/form-container';
import { Button } from '@/app/components/ui/button';

import { 
  PartFormProps,
  PartFormData,
  partFormSchema 
} from './types';

/**
 * Client component implementation of PartForm
 * Handles form validation, submission, and rendering
 */
export default function PartFormClient({
  onSubmit,
  onClose,
  initialData,
  isEdit = false,
  title
}: PartFormProps) {
  // ... implementation from current component
  // ... with all the form handling logic
}
```

### index.ts
```typescript
export { PartForm } from './PartForm';
export type { PartFormData, PartFormProps } from './types';
```

## Benefits of Refactoring

1. **Performance Optimization**
   - Server component can be rendered on the server
   - Only interactive parts need to be handled on the client

2. **Code Organization**
   - Better separation of concerns
   - Types are isolated for better maintainability

3. **Bundle Size Reduction**
   - Client-side JavaScript is reduced
   - Only form handling code is sent to the browser

4. **Improved Type Safety**
   - Centralized type definitions
   - Consistent interface across components

## Next Steps

After refactoring PartForm:
1. Update imports in all files that use PartForm
2. Test thoroughly to ensure functionality is preserved
3. Apply the same pattern to ProductForm
4. Update documentation to reflect the new component structure 