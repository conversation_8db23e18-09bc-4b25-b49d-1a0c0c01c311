# Documentation Standards

This document outlines the documentation standards to follow in the Trend IMS project to ensure consistency and maintainability.

## Code Documentation

### JSDoc Comments

Use JSDoc comments to document functions, classes, interfaces, and other code elements:

```tsx
/**
 * Formats a date to a readable string
 * @param date - The date to format
 * @param format - The format to use (optional)
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string, format?: string): string => {
  // Implementation
};
```

### Component Documentation

Document React components with JSDoc comments:

```tsx
/**
 * A card component for displaying product information
 * Supports different color schemes and animations
 */
const ProductCard: React.FC<ProductCardProps> = ({ product, onSelect }) => {
  // Implementation
};
```

### Interface Documentation

Document interfaces and types with JSDoc comments:

```tsx
/**
 * Props for the ProductCard component
 */
interface ProductCardProps {
  /** The product to display */
  product: Product;
  /** Function called when the card is selected */
  onSelect: (id: string) => void;
  /** Color scheme for the card */
  color?: 'blue' | 'green' | 'red';
  /** Whether to animate the card */
  animate?: boolean;
}
```

### Context Documentation

Document context providers and hooks:

```tsx
/**
 * Context for managing product data
 * Provides functions for fetching, creating, updating, and deleting products
 */
const ProductContext = createContext<ProductContextType | undefined>(undefined);

/**
 * Provider component for the product context
 * Manages the state of products and provides methods for CRUD operations
 */
export const ProductProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Implementation
};

/**
 * Custom hook to access the product context
 * Must be used within a ProductProvider component
 * @returns The product context with all data and functions
 * @throws Error if used outside of a ProductProvider
 */
export const useProductContext = () => {
  // Implementation
};
```

## File Documentation

### README Files

Each directory should have a README.md file that explains the purpose of the directory and its contents:

```markdown
# Components Directory

This directory contains all React components used throughout the application, organized by their purpose and functionality.

## Directory Structure

- **ui/**: Basic UI components that are reused across the application
- **features/**: Feature-specific components
- **layout/**: Layout components like headers, footers, and sidebars
- **forms/**: Form components for data entry
- **charts/**: Chart and visualization components

## Best Practices

1. **Component Naming**: Use PascalCase for component names
2. **Props Interface**: Define a clear interface for component props
3. **Default Exports**: Use default exports for components
4. **Client Directive**: Add "use client" directive at the top of client components
```

### Module Documentation

Add a comment at the top of each file explaining its purpose:

```tsx
/**
 * Utility functions for formatting data
 * Includes functions for formatting dates, currencies, and strings
 */

export const formatDate = (date: Date | string): string => {
  // Implementation
};
```

## Documentation in Code

### Comments

Use comments to explain complex logic or non-obvious code:

```tsx
// Calculate the total value of inventory by multiplying quantity by price
// and summing across all products
const totalValue = products.reduce((sum, product) => {
  return sum + (product.quantity * product.price);
}, 0);
```

### TODO Comments

Use TODO comments to mark areas that need improvement:

```tsx
// TODO: Optimize this calculation for large datasets
const filteredProducts = products.filter(product => {
  return product.category === selectedCategory;
});
```

## Documentation Tools

### TypeScript

Use TypeScript to document the shape of data and function signatures:

```tsx
interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  inStock: boolean;
}

function getProductById(id: string): Product | undefined {
  // Implementation
}
```

### PropTypes

For JavaScript files, use PropTypes to document component props:

```jsx
import PropTypes from 'prop-types';

function ProductCard({ product, onSelect }) {
  // Implementation
}

ProductCard.propTypes = {
  product: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
  }).isRequired,
  onSelect: PropTypes.func.isRequired,
};
```

## Examples

### Component Example

```tsx
"use client";

/**
 * StatusCard component for displaying status information
 * Shows a main statistic and a breakdown of status categories
 */
import React from 'react';
import { motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import BaseCard from './BaseCard';

/**
 * Props for the StatusCard component
 */
interface StatusCardProps {
  /** Title of the card */
  title: string;
  /** Data to display as key-value pairs */
  data: Record<string, number>;
  /** Main statistic to highlight */
  mainStat: {
    /** Value of the main statistic */
    value: number;
    /** Label for the main statistic */
    label: string;
  };
  /** Color scheme for the card */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Icon to display */
  icon?: React.ReactNode;
  /** Function called when the card is clicked */
  onClick?: () => void;
}

/**
 * StatusCard component for displaying status information
 * Shows a main statistic and a breakdown of status categories
 */
const StatusCard: React.FC<StatusCardProps> = ({
  title,
  data,
  mainStat,
  color = 'blue',
  icon,
  onClick
}) => {
  const { theme } = useTheme();
  
  // Implementation
};

export default StatusCard;
```

### Utility Example

```tsx
/**
 * Date formatting utilities
 * Provides functions for formatting dates in various formats
 */

/**
 * Format a date to a readable string
 * @param date - The date to format
 * @param format - The format to use (optional)
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string, format?: string): string => {
  if (!date) return '';
  const d = typeof date === 'string' ? new Date(date) : date;
  
  // Default format: "Jan 1, 2023"
  if (!format) {
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
  
  // Custom format implementation
  // TODO: Implement custom format support
  return d.toLocaleDateString();
};

/**
 * Get a relative time string (e.g., "2 hours ago")
 * @param date - The date to format
 * @returns Relative time string
 */
export const getRelativeTime = (date: Date | string): string => {
  // Implementation
};
```
