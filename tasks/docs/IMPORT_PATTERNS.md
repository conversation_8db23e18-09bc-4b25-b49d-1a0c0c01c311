# Import Patterns Guide

This document outlines the recommended import patterns to use in the Trend IMS project to ensure consistency and maintainability.

## Path Aliases

The project is configured with path aliases to make imports more consistent and easier to manage. The main path alias is `@/app`, which points to the `app` directory.

### Using Path Aliases

Instead of using relative imports like this:
```tsx
import { useTheme } from '../../context/ThemeContext';
import { Product } from '../types';
```

Use path aliases like this:
```tsx
import { useTheme } from '@/app/context/ThemeContext';
import { Product } from '@/app/types';
```

## Import Order

Organize imports in the following order:

1. React and Next.js imports
2. Third-party library imports
3. Path alias imports (from most general to most specific)
4. Relative imports (if necessary)
5. CSS/SCSS imports

Example:
```tsx
// 1. React and Next.js imports
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

// 2. Third-party library imports
import { motion } from 'framer-motion';
import { Package, Settings } from 'lucide-react';

// 3. Path alias imports
import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';
import { Product } from '@/app/types';
import { formatDate } from '@/app/utils/helpers';

// 4. Relative imports (if necessary)
import styles from './Component.module.css';
```

## Import Types

When importing types, prefer importing them from the central types directory:

```tsx
// Preferred
import { Product, OrderStatus } from '@/app/types';

// Avoid
import { Product } from '@/app/types/inventory';
import { OrderStatus } from '@/app/types/orders';
```

This works because the `app/types/index.ts` file re-exports all types from subdirectories.

## Component Imports

When importing components, use the full path to the component:

```tsx
// Preferred
import BaseCard from '@/app/components/ui/BaseCard';
import ProductTable from '@/app/components/features/ProductTable';

// Avoid
import { BaseCard, ProductTable } from '@/app/components';
```

This makes it easier to track dependencies and improves code readability.

## Context Imports

When importing context hooks, use the full path to the context file:

```tsx
// Preferred
import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';

// Avoid
import { useAppContext, useTheme } from '@/app/context';
```

## Utility Imports

When importing utility functions, use the full path to the utility file:

```tsx
// Preferred
import { formatDate, formatCurrency } from '@/app/utils/helpers';

// Avoid
import { formatDate, formatCurrency } from '@/app/utils';
```

## Examples

### Component File

```tsx
"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Package, Settings } from 'lucide-react';

import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';
import { Product } from '@/app/types';
import { formatDate } from '@/app/utils/helpers';

import BaseCard from '@/app/components/ui/BaseCard';

interface ProductCardProps {
  product: Product;
  onSelect: (id: string) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onSelect }) => {
  // Component implementation
};

export default ProductCard;
```

### Context File

```tsx
"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

import { Product } from '@/app/types';
import { supabase } from '@/app/lib/supabase';

interface ProductContextType {
  products: Product[];
  isLoading: boolean;
  error: string | null;
  fetchProducts: () => Promise<void>;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const ProductProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Provider implementation
};

export const useProductContext = () => {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProductContext must be used within a ProductProvider');
  }
  return context;
};
```

### Utility File

```tsx
/**
 * Format a date to a readable string
 * @param date The date to format
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string): string => {
  if (!date) return '';
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Format a currency value
 * @param value The value to format
 * @param currency The currency code (default: USD)
 * @returns Formatted currency string
 */
export const formatCurrency = (value: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(value);
};
```
