# MongoDB Implementation Guide

This document provides details on how MongoDB is implemented in the Trend IMS application using Next.js 15.2.4 and React 19.1.0 best practices.

## Connection Utility

The MongoDB connection is managed through a centralized utility in `app/lib/mongodb.ts`. This utility follows the latest Next.js best practices for database connections:

```typescript
import mongoose from 'mongoose';

/**
 * Type definition for the cached mongoose connection
 */
interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

// Extend the global type to include our mongoose cache
declare global {
  var mongoose: MongooseCache;
}

// Get MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI;

// Initialize or retrieve the cached connection
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

/**
 * Connects to MongoDB using Mongoose with connection caching.
 */
async function connectToDatabase(): Promise<typeof mongoose> {
  // Return existing connection if available
  if (cached.conn) {
    console.log('[MongoDB] Using cached connection');
    return cached.conn;
  }

  // Create new connection promise if none exists
  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };

    cached.promise = mongoose.connect(MONGODB_URI!, opts)
      .then(mongoose => {
        console.log('[MongoDB] Connection established successfully');
        return mongoose;
      })
      .catch(error => {
        console.error('[MongoDB] Connection error:', error);
        cached.promise = null;
        throw error;
      });
  }

  try {
    // Wait for the connection promise to resolve
    cached.conn = await cached.promise;
    return cached.conn;
  } catch (error) {
    // Reset promise on error
    cached.promise = null;
    throw error;
  }
}

export default connectToDatabase;
```

## Key Features

1. **Connection Caching**
   - Uses a singleton pattern to cache the MongoDB connection
   - Prevents multiple connections during development hot-reloading
   - Efficient in serverless environments by reusing connections

2. **Error Handling**
   - Robust error handling for connection failures
   - Specific handling for validation errors and duplicate key errors
   - Proper promise rejection handling

3. **Environment Variables**
   - Connection string stored in environment variables for security
   - No hardcoded connection strings in the codebase
   - Clear error messages when required variables are missing

4. **TypeScript Integration**
   - Proper type definitions for the connection cache
   - Global type extensions for the mongoose cache

## Mongoose Models

The application uses Mongoose models for data validation and schema enforcement. Models are defined in the `app/models` directory:

- `part.model.ts` - Parts and components
- `inventory.model.ts` - Inventory levels and stock information
- `supplier.model.ts` - Supplier information
- `assembly.model.ts` - Assembly definitions and bill of materials
- `purchaseOrder.model.ts` - Purchase order tracking

Example model definition:

```typescript
import mongoose, { Schema, Document } from 'mongoose';

export interface IPart extends Document {
  part_id: string;
  name: string;
  description?: string;
  technical_specs?: string;
  is_external: boolean;
  supplier_id?: string;
  barcode_sku?: string;
  manufacturer_part_number?: string;
  unit_of_measure?: string;
  minimum_order_quantity?: number;
  lead_time_days?: number;
  created_at: Date;
  updated_at: Date;
}

const PartSchema: Schema = new Schema({
  part_id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String },
  technical_specs: { type: String },
  is_external: { type: Boolean, default: false },
  supplier_id: { type: Schema.Types.ObjectId, ref: 'Supplier' },
  barcode_sku: { type: String },
  manufacturer_part_number: { type: String },
  unit_of_measure: { type: String },
  minimum_order_quantity: { type: Number },
  lead_time_days: { type: Number },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
}, {
  timestamps: {
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
});

// Ensure the model won't be recompiled multiple times
export const Part = mongoose.models.Part || mongoose.model<IPart>('Part', PartSchema);

export default Part;
```

## API Routes

The application uses Next.js API routes to interact with MongoDB. Each route follows a consistent pattern:

1. Import the connection utility and models
2. Establish a connection to MongoDB
3. Perform database operations
4. Return the results as JSON

Example API route:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/app/lib/mongodb';
import { Part, Inventory } from '@/app/models';

export async function GET(_request: NextRequest) {
  try {
    console.log('[API] GET /api/parts - Fetching parts');

    // Ensure database connection is established
    await connectToDatabase();

    // Fetch parts with supplier information
    const parts = await Part.find()
      .populate({
        path: 'supplier_id',
        select: 'name contact_person phone email performance_rating'
      })
      .lean();

    // Get inventory data separately and merge
    const inventoryData = await Inventory.find().lean();

    // Create a map for quick lookup
    const inventoryMap = inventoryData.reduce((map: Record<string, any>, item: any) => {
      map[item.part_id.toString()] = item;
      return map;
    }, {});

    // Merge part and inventory data
    const mergedData = parts.map((part: any) => {
      const inventory = inventoryMap[part._id.toString()] || {};

      return {
        ...part,
        inventory: {
          current_stock: inventory.current_stock || 0,
          reorder_level: inventory.reorder_level || 0,
          location: inventory.location || '',
          last_count_date: inventory.last_inventory_count_date || null,
          maximum_stock_level: inventory.maximum_stock_level || 0,
          safety_stock_level: inventory.safety_stock_level || 0,
          average_daily_usage: inventory.average_daily_usage || 0,
          abc_classification: inventory.abc_classification || 'C'
        }
      };
    });

    console.log(`[API] Fetched ${mergedData.length} parts with inventory data`);
    return NextResponse.json({ data: mergedData, error: null });

  } catch (error) {
    console.error('[API] Error fetching parts:', error);
    return NextResponse.json(
      {
        data: null,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}
```

## Database Seeding

The application includes a script for seeding the database with initial data. This script is located in `init-mongodb.cjs` and can be run with:

```bash
npm run seed-db-dev
```

The script:
1. Connects to MongoDB
2. Clears existing data
3. Creates sample suppliers, parts, inventory, and assemblies

This is useful for development and testing purposes, but should not be used in production.

## Recent Updates

The following updates have been made to improve the MongoDB implementation:

1. **Removed Hardcoded Connection Strings**
   - Removed fallback connection string from `app/lib/mongodb.ts`
   - Updated `test-db-connection.cjs` to require a valid connection string
   - Added proper error handling for missing connection strings

2. **Improved Environment Configuration**
   - Updated `create-env.cjs` to check for MongoDB credentials
   - Created a template for `.env.local` with MongoDB configuration
   - Added validation for required environment variables

3. **Enhanced Testing and Verification**
   - Created new `verify-mongodb-data.cjs` script to verify data in the database
   - Added comprehensive error handling and reporting
   - Improved logging for database operations

4. **Clarified Development vs. Production**
   - Renamed `seed-db` script to `seed-db-dev` to make it clear it's for development only
   - Added warnings about using sample data in production
   - Created clear documentation about setting up real MongoDB connections

## Best Practices

1. **Server-Side Only**
   - All database operations are performed on the server side
   - Connection strings and credentials are never exposed to the client

2. **Connection Pooling**
   - MongoDB driver automatically manages connection pooling
   - The connection utility ensures efficient reuse of connections

3. **Error Handling**
   - All database operations are wrapped in try/catch blocks
   - Specific error types are handled appropriately
   - Errors are logged for debugging

4. **Data Validation**
   - Mongoose schemas enforce data validation
   - API routes validate input data before database operations

5. **Lean Queries**
   - The `.lean()` method is used for read operations to improve performance
   - This returns plain JavaScript objects instead of Mongoose documents

6. **Populate References**
   - Mongoose's `.populate()` method is used to resolve references between collections
   - This provides a clean way to work with related data
