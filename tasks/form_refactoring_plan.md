# Form Component Refactoring Plan

## Current Analysis

The Form component should be refactored to the server/client pattern because:

1. It uses "use client" directive
2. It uses various React hooks (useContext, useFormContext, useId)
3. It leverages Radix UI components (Slot, LabelPrimitive)
4. It contains several sub-components (FormField, FormItem, FormLabel, etc.)
5. It integrates with react-hook-form, which is a client-side form library

## Refactoring Approach

The Form component is more complex than previous components we've refactored because:
- It exports multiple related components (Form, FormItem, FormLabel, etc.)
- It integrates with react-hook-form
- It uses contexts for state sharing between sub-components

For this component, we'll need to keep most of the implementation in the client component since it's heavily dependent on client-side features, and the server component will primarily serve as a re-export mechanism.

## Refactoring Steps

### 1. Create File Structure

Create the following directory structure:
```
app/components/ui/Form/
├── index.ts
├── Form.tsx (Server Component)
├── FormClient.tsx (Client Component)
├── types.ts
└── README.md
```

### 2. Define Types (types.ts)

Extract the form-related types and contexts:
```typescript
import * as React from "react";
import { FieldPath, FieldValues } from "react-hook-form";

/**
 * Form Field Context Value type
 */
export type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
  name: TName;
};

/**
 * Form Item Context Value type
 */
export type FormItemContextValue = {
  id: string;
};

/**
 * Context for form fields
 */
export const FormFieldContext = React.createContext<FormFieldContextValue>(
  {} as FormFieldContextValue
);

/**
 * Context for form items
 */
export const FormItemContext = React.createContext<FormItemContextValue>(
  {} as FormItemContextValue
);
```

### 3. Implement Client Component (FormClient.tsx)

Move the existing implementation to FormClient.tsx, organizing the exports:
```typescript
"use client";

import * as React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";
import { Slot } from "@radix-ui/react-slot";
import {
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  FormProvider,
  useFormContext,
} from "react-hook-form";

import { cn } from "@/app/lib/utils";
import { Label } from "@/app/components/ui/label";
import { FormFieldContext, FormItemContext, FormFieldContextValue, FormItemContextValue } from "./types";

// Form Provider
export const FormClient = FormProvider;

// FormField component
export const FormFieldClient = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  ...props
}: ControllerProps<TFieldValues, TName>) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};

// useFormField hook
export const useFormFieldClient = () => {
  const fieldContext = React.useContext(FormFieldContext);
  const itemContext = React.useContext(FormItemContext);
  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error("useFormField should be used within <FormField>");
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

// FormItem component
export const FormItemClient = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const id = React.useId();

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn("space-y-2", className)} {...props} />
    </FormItemContext.Provider>
  );
});
FormItemClient.displayName = "FormItem";

// FormLabel component
export const FormLabelClient = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>
>(({ className, ...props }, ref) => {
  const { error, formItemId } = useFormFieldClient();

  return (
    <Label
      ref={ref}
      className={cn(error && "text-destructive", className)}
      htmlFor={formItemId}
      {...props}
    />
  );
});
FormLabelClient.displayName = "FormLabel";

// FormControl component
export const FormControlClient = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } = useFormFieldClient();

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={
        !error
          ? `${formDescriptionId}`
          : `${formDescriptionId} ${formMessageId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  );
});
FormControlClient.displayName = "FormControl";

// FormDescription component
export const FormDescriptionClient = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormFieldClient();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn("text-sm text-muted-foreground dark:text-text-secondary", className)}
      {...props}
    />
  );
});
FormDescriptionClient.displayName = "FormDescription";

// FormMessage component
export const FormMessageClient = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormFieldClient();
  const body = error ? String(error?.message) : children;

  if (!body) {
    return null;
  }

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn("text-sm font-medium text-destructive dark:text-destructive", className)}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessageClient.displayName = "FormMessage";
```

### 4. Implement Server Component (Form.tsx)

Create a wrapper that re-exports the client components:
```typescript
// This is a Server Component by default (no "use client" directive)
import {
  FormClient,
  FormFieldClient,
  FormItemClient,
  FormLabelClient,
  FormControlClient,
  FormDescriptionClient,
  FormMessageClient,
  useFormFieldClient,
} from './FormClient';

/**
 * Form components for building forms with react-hook-form
 * Delegates to client components since they use React hooks and contexts
 */

export const Form = FormClient;
export const FormField = FormFieldClient;
export const FormItem = FormItemClient;
export const FormLabel = FormLabelClient;
export const FormControl = FormControlClient;
export const FormDescription = FormDescriptionClient;
export const FormMessage = FormMessageClient;
export const useFormField = useFormFieldClient;
```

### 5. Create Index File (index.ts)

```typescript
// Export form components
export {
  useFormField,
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
} from './Form';
```

### 6. Create Documentation (README.md)

Document:
- Form component structure
- Integration with react-hook-form
- Usage examples for each component
- Common form patterns

## Implementation Benefits

1. **Better Organization**
   - Separation of client-side logic
   - Clearer component structure

2. **Type Safety**
   - Explicit types for context values
   - Better IntelliSense support

3. **Consistency**
   - Follows established pattern across the codebase
   - Documentation in line with other components

## Testing Plan

1. Test with basic form implementation
2. Verify validation and error states
3. Test with various input types
4. Confirm dark mode styling works 