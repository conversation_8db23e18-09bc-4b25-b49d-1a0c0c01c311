# Next.js Files to Update

## Configuration Files
- next.config.mjs
- tailwind.config.js
- postcss.config.js

## Root App Components
- app/layout.tsx
- app/page.tsx
- app/providers.tsx
- app/metadata.ts

## Layouts
- app/(main)/layout.tsx
- app/dashboard/layout.tsx

## Page Components
- app/(main)/page.tsx
- app/(main)/suppliers/page.tsx
- app/(main)/hierarchical-part-entry/page.tsx
- app/(main)/reports/page.tsx
- app/(main)/analytics/page.tsx
- app/(main)/logistics/page.tsx
- app/(main)/categories/page.tsx
- app/(main)/user-management/page.tsx
- app/(main)/batch-tracking/page.tsx
- app/(main)/product-import/page.tsx
- app/(main)/product/[id]/page.tsx
- app/(main)/inventory-transactions/page.tsx
- app/(main)/warehouses/page.tsx
- app/(main)/purchase-orders/page.tsx
- app/(main)/settings/page.tsx
- app/(main)/work-orders/page.tsx
- app/dashboard/page.tsx
- app/test-harness/page.tsx

## Context Providers
- app/context/AppContext.tsx
- app/context/MockAuthContext.tsx
- app/context/ThemeContext.tsx

## UI Components
- app/components/ui/label.tsx
- app/components/ui/ErrorDisplay.tsx
- app/components/ui/input.tsx
- app/components/ui/form.tsx
- app/components/ui/textarea.tsx
- app/components/ui/ActionCard.tsx
- app/components/ui/dropdown-menu.tsx
- app/components/ui/select.tsx
- app/components/ui/StatusCard.tsx
- app/components/ui/BaseCard.tsx
- app/components/ui/ThemeToggle.tsx
- app/components/ui/button.tsx
- app/components/ui/tooltip.tsx
- app/components/ui/CalendarComponent.tsx
- app/components/ui/tabs.tsx
- app/components/ui/form-container.tsx

## Layout Components
- app/components/layout/HeaderRightControls.tsx
- app/components/layout/Sidebar.tsx
- app/components/layout/Header.tsx

## Feature Components
- app/components/features/AssemblyStatus.tsx
- app/components/features/ProductComponentsList.tsx
- app/components/features/BomViewer.tsx
- app/components/features/ProductTable.tsx
- app/components/features/ProductModal.tsx
- app/components/features/ProductCard.tsx
- app/components/features/DatabaseStatus.tsx
- app/components/features/ThemeShowcase.tsx
- app/components/features/SupabaseSetup.tsx
- app/components/features/LogisticsMap.tsx
- app/components/features/ProductHierarchy.tsx

## Form Components
- app/components/EnhancedPartForm.tsx
- app/components/forms/ProductForm.tsx
- app/components/forms/ProductModal.tsx
- app/components/forms/EnhancedPartForm.tsx
- app/components/forms/PartForm.tsx
- app/components/forms/HierarchicalPartsForm.tsx

## Table Components
- app/components/tables/ProductsTable.tsx
- app/components/tables/AssembliesTable.tsx

## Chart Components
- app/components/charts/AssemblyStatus.tsx
- app/components/charts/CategoryDistribution.tsx
- app/components/charts/ProductionPlanning.tsx
- app/components/charts/LogisticsMap.tsx
- app/components/charts/CalendarComponent.tsx
- app/components/charts/ProductionCapacity.tsx

## Search Components
- app/components/search/PartSearch.tsx

## Type Definitions
- app/types/orders/index.ts
- app/types/user/index.ts
- app/types/index.ts
- app/types/forms/index.ts
- app/types/inventory/index.ts

## Test Files (where applicable)
- app/components/ui/StatusCard.test.tsx
- app/components/ui/BaseCard.test.tsx
- app/components/ui/ActionCard.test.tsx
- app/components/layout/Header.test.tsx 