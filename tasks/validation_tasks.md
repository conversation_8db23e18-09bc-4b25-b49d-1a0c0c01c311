# Validation Tasks

## Overview
This document tracks tasks related to improving the validation logic and user experience in the HierarchicalPartsForm component. The primary focus is on enhancing validation, error handling, and user feedback for form submissions.

## Current Status
- Started on: 2023-08-25
- Priority: High
- Owner: Development Team
- Status: In Progress

## Tasks

### 1. Analysis and Investigation

#### 1.1 Current Implementation Review
- **Priority**: High
- **Status**: Completed
- **Description**: Examine the current `validateHierarchy` function to understand its implementation and limitations.
- **Notes**: The function validates emptiness, required fields, and circular dependencies.

#### 1.2 Edge Case Identification
- **Priority**: High
- **Status**: Completed
- **Description**: Identify potential edge cases that aren't handled in the current validation logic.
- **Notes**: Analysis complete. Found duplicate ID check logic was flawed; refactored to use Set correctly during recursive traversal. Circular dependency, depth, and self-reference checks appear robust.

#### 1.3 Validation Testing
- **Priority**: Medium
- **Status**: In Progress
- **Description**: Test validation with complex hierarchical structures to ensure robustness.
- **Notes**: Created test cases for various hierarchy shapes and edge conditions. See 'Test Cases' section below.

### 2. Validation Logic Improvements

#### 2.1 Enhanced Error Messages
- **Priority**: High
- **Status**: In Progress
- **Description**: Improve error messages to provide clearer guidance to users.
- **Notes**: Include specific information about which part has issues and what needs to be fixed. (Updated message content in `validatePart` function).

#### 2.2 Duplicate Part ID Validation
- **Priority**: High
- **Status**: In Progress
- **Description**: Add validation to prevent duplicate part IDs across different levels of the hierarchy.
- **Notes**: Implemented helper function `isPartIdDuplicate` and added checks in `updatePart` (strict prevention) and `handlePartSelected`. Core `validateHierarchy` duplicate check also refactored for correctness.

#### 2.3 Circular Dependency Detection
- **Priority**: Medium
- **Status**: To Do
- **Description**: Enhance the circular dependency detection to be more robust.
- **Notes**: Current implementation might miss complex circular references.

#### 2.4 Maximum Parts Validation
- **Priority**: Low
- **Status**: To Do
- **Description**: Add validation for maximum number of parts in a single level.
- **Notes**: Consider performance implications of very large hierarchies.

### 3. User Experience Enhancements

#### 3.1 Visual Error Indicators
- **Priority**: High
- **Status**: In Progress
- **Description**: Highlight problematic parts in the UI when validation fails.
- **Notes**: Enhanced error badge with tooltip showing specific errors (Missing ID/Name/Qty).

#### 3.2 Inline Validation
- **Priority**: Medium
- **Status**: In Progress
- **Description**: Implement inline validation feedback as users type.
- **Notes**: Added `inlineErrors` state and `validateField` function. Updated `onChange` handlers and rendering logic for Part ID, Name, and Quantity fields to show immediate errors.

#### 3.3 Form Navigation
- **Priority**: Medium
- **Status**: To Do
- **Description**: Add functionality to automatically navigate to problematic fields.
- **Notes**: Focus on the first invalid field when validation fails.

### 4. Performance Optimization

#### 4.1 Validation Performance
- **Priority**: Low
- **Status**: To Do
- **Description**: Optimize recursive validation performance for large hierarchies.
- **Notes**: Consider memoization or more efficient traversal algorithms.

#### 4.2 Validation Caching
- **Priority**: Low
- **Status**: To Do
- **Description**: Implement caching for validation results to avoid redundant checks.
- **Notes**: Only re-validate parts that have changed.

## Dependencies
- Task 2.1 depends on 1.2
- Task 3.1 depends on 2.1 and 2.2
- Task 3.2 depends on 2.1
- Task 4.2 depends on 4.1

## Progress Tracking
- [ ] Task Group 1: Analysis (33% complete)
- [ ] Task Group 2: Logic Improvements (0% complete)
- [ ] Task Group 3: UX Enhancements (0% complete)
- [ ] Task Group 4: Performance (0% complete)

## Notes
- Consider adding unit tests specifically for validation functions
- Need to coordinate with UI team for visual indicator implementation
- Performance testing should be conducted with large assemblies (100+ parts)

## Test Cases (Task 1.3)

This section outlines scenarios to test the `validateHierarchy` function.

### Duplicate Part IDs

1.  **Scenario:** Two root parts with the same ID.
    *   **Hierarchy:** `Root(ID:A), Root(ID:A)`
    *   **Expected:** Error `Duplicate Part ID: "A" is used more than once...`
2.  **Scenario:** Root and grandchild with the same ID.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:B) -> Grandchild(ID:A)`
    *   **Expected:** Error `Duplicate Part ID: "A" is used more than once...`
3.  **Scenario:** Siblings with the same ID.
    *   **Hierarchy:** `Root(ID:P) -> Child(ID:C), Child(ID:C)`
    *   **Expected:** Error `Duplicate Part ID: "C" is used more than once...`
4.  **Scenario:** Changing an existing part's ID to one already present.
    *   **Initial:** `Root(ID:X), Root(ID:Y)`
    *   **Action:** Change ID of Root Y to X.
    *   **Expected:** Inline error `Part ID "X" is already in use.` (from `updatePart`), and submission validation error `Duplicate Part ID: "X"...`

### Circular Dependencies

5.  **Scenario:** Simple A -> B -> A cycle.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:B) -> Grandchild(ID:A)`
    *   **Expected:** Error `Circular Dependency: Part "A" creates a loop (B → A)` (Detected on Grandchild A)
6.  **Scenario:** Longer A -> B -> C -> A cycle.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:B) -> Grandchild(ID:C) -> GreatGrandchild(ID:A)`
    *   **Expected:** Error `Circular Dependency: Part "A" creates a loop (B → C → A)` (Detected on GreatGrandchild A)
7.  **Scenario:** Self-reference A -> A.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:A)`
    *   **Expected:** Error `Self-Reference: Part "A" cannot be its own child.`

### Depth Limit

8.  **Scenario:** Hierarchy exceeding maxDepth (default 10).
    *   **Hierarchy:** `L0 -> L1 -> L2 -> ... -> L10 -> L11`
    *   **Expected:** Error `Assembly structure is too deep. Maximum depth is 10 levels.`

### Mixed Scenarios

9.  **Scenario:** Duplicate ID and Circular Dependency.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:B), Root(ID:B) -> Child(ID:A)`
    *   **Expected:** Errors `Duplicate Part ID: "B"...`, `Circular Dependency: Part "A" creates a loop (B → A)`
10. **Scenario:** Valid deep structure using a part multiple times.
    *   **Hierarchy:** `Root(ID:X) -> Asm1(ID:A) -> Part(ID:P), Root(ID:Y) -> Asm2(ID:B) -> Part(ID:P)`
    *   **Expected:** No errors.

### Empty/Invalid Fields

11. **Scenario:** Part deep in hierarchy missing Name.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:B) -> Grandchild(ID:C, Name:"")`
    *   **Expected:** Error `Part with ID "C" must have a Name specified.`
12. **Scenario:** Part deep in hierarchy with quantity 0.
    *   **Hierarchy:** `Root(ID:A) -> Child(ID:B) -> Grandchild(ID:C, Qty:0)`
    *   **Expected:** Error `Quantity for Part "C" must be at least 1.` 