# MongoDB Setup Guide

This guide provides instructions on how to set up and use MongoDB with the Trend IMS application.

## Prerequisites

- MongoDB Atlas account or a local MongoDB server
- Node.js and npm installed

## Setting Up MongoDB Connection

### 1. Create a MongoDB Atlas Cluster (Recommended)

1. Sign up for a free MongoDB Atlas account at [https://www.mongodb.com/cloud/atlas](https://www.mongodb.com/cloud/atlas)
2. Create a new cluster (the free tier is sufficient for development)
3. Set up a database user with read/write permissions
4. Configure network access to allow connections from your IP address
5. Get your connection string from the "Connect" button in the Atlas dashboard

### 2. Configure Environment Variables

1. Create a `.env.local` file in the root directory of the project (if it doesn't exist already)
2. Add your MongoDB connection string to the file:

```
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster-url>/<database-name>?retryWrites=true&w=majority
MONGODB_DB_NAME=Trend_IMS
```

Replace `<username>`, `<password>`, `<cluster-url>`, and `<database-name>` with your actual MongoDB credentials.

### 3. Verify MongoDB Connection

Run the following command to verify that your MongoDB connection is working:

```bash
npm run test-db
```

This will attempt to connect to your MongoDB database and display the connection status.

### 4. Verify MongoDB Data

Run the following command to verify that your MongoDB database has the necessary data:

```bash
npm run verify-db-data
```

This will connect to your MongoDB database, list all collections, and display a sample document from each collection.

## Using MongoDB in the Application

The application uses MongoDB for data storage with the latest Next.js 15.2.4 and React 19.1.0 best practices:

### Connection Implementation

- **Connection Caching**: The application uses a singleton pattern to cache the MongoDB connection, preventing multiple connections during development hot-reloading and in serverless environments.

- **Error Handling**: Robust error handling for database operations with specific handling for validation errors and duplicate key errors.

- **Environment Variables**: The MongoDB connection string is stored in environment variables for security.

- **Mongoose Models**: The application uses Mongoose models for data validation and schema enforcement.

### API Routes

All API routes use the centralized MongoDB connection utility in `app/lib/mongodb.ts`:

- `/api/parts` - CRUD operations for parts and inventory
- `/api/suppliers` - CRUD operations for suppliers
- `/api/assemblies` - CRUD operations for assemblies
- `/api/purchase-orders` - CRUD operations for purchase orders

## Development Data Seeding (Optional)

For development purposes only, you can seed your database with sample data using the following command:

```bash
npm run seed-db-dev
```

**Note**: This is for development only and should not be used in production. In production, you should use real data.

## Troubleshooting

### Connection Issues

If you're having trouble connecting to MongoDB, check the following:

1. Verify that your MongoDB connection string is correct
2. Ensure that your IP address is whitelisted in MongoDB Atlas
3. Check that your database user has the correct permissions
4. Verify that your MongoDB Atlas cluster is running

### Data Issues

If you're having trouble with data in your MongoDB database, check the following:

1. Run `npm run verify-db-data` to check if your database has the necessary collections and documents
2. If your database is empty, you can seed it with sample data using `npm run seed-db-dev` (for development only)
3. Check the console logs for any errors related to MongoDB operations
