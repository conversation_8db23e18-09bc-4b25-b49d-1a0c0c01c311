# Project Dependencies Requirements

## Node.js Version
- Requires Node.js version ^18.18.0 || ^19.8.0 || >= 20.0.0 (based on Next.js requirements)

## Core Framework Requirements
- Next.js 15.2.4
- React 19.1.0
- React DOM 19.1.0
- TypeScript 5.x

## Database Requirements
- MongoDB 6.15.0
- Mongoose 8.13.1

## UI Component Libraries
- Radix UI components (various at ^1.x and ^2.x)
- Lucide React 0.344.0 (for icons)
- Framer Motion 11.0.8 (for animations)
- Recharts 2.12.2 (for charts)
- Class Variance Authority 0.7.1
- Tailwind CSS 3.4.1

## Form Handling
- React Hook Form 7.54.2
- @hookform/resolvers 5.0.1
- Zod 3.24.2 (for schema validation)

## Utility Libraries
- Lodash 4.17.21
- UUID 11.1.0
- date-fns 3.3.1
- clsx 2.1.1
- tailwind-merge 3.2.0

## Development Dependencies
### Testing
- Jest 29.7.0
- @testing-library/react 16.3.0
- @testing-library/jest-dom 6.6.3
- @testing-library/dom 10.4.0

### Linting & Type Checking
- ESLint 8.x
- TypeScript ESLint 8.3.0
- Various ESLint plugins for React

### CSS Processing
- PostCSS 8.4.35
- Autoprefixer 10.4.18
- Tailwind Scrollbar 3.0.5

## Environment Requirements
- Supports SSL/TLS connections (NODE_TLS_REJECT_UNAUTHORIZED configurations present)
- Requires environment variable setup (dotenv 16.4.7)

## Browser Compatibility
- Modern browsers supporting ES5+ features
- DOM and DOM.Iterable APIs
- ESNext features

## Development Environment
- Git (version control)
- npm or yarn (package management)
- Development server runs on port 5173 by default