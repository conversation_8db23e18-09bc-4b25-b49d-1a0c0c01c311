# Dark Mode Testing Strategy

## Overview

Based on our audit of the theme implementation across the codebase, we've identified that the application uses Tailwind CSS with class-based dark mode and a ThemeContext provider that manages theme state. This document outlines a comprehensive testing strategy to ensure the dark mode implementation works correctly across all components and pages.

## Testing Goals

1. Verify that all components render correctly in both light and dark modes
2. Ensure theme toggling works properly and persists across page navigation
3. Validate that theme-specific styles are applied correctly
4. Check for accessibility issues in both themes (color contrast, focus visibility)
5. Test response to system preference changes

## Testing Approach

### 1. Automated Visual Testing

#### Puppeteer Script Enhancements

We'll enhance the existing `dark-mode-test.js` Puppeteer script to:

```javascript
// Expand the existing Puppeteer test to cover more components and pages
const puppeteer = require('puppeteer');

(async () => {
  console.log('Starting comprehensive dark mode tests...');
  const browser = await puppeteer.launch({ 
    headless: false, 
    args: ['--window-size=1280,800'],
    defaultViewport: { width: 1280, height: 800 }
  });

  const page = await browser.newPage();
  
  // Pages to test
  const pagesToTest = [
    { route: '/', name: 'Dashboard' },
    { route: '/inventory', name: 'Inventory' },
    { route: '/products', name: 'Products' },
    { route: '/assemblies', name: 'Assemblies' },
    { route: '/reports', name: 'Reports' },
    { route: '/settings', name: 'Settings' }
  ];
  
  // Components to specifically test for theme correctness
  const componentsToTest = [
    { selector: '.main-header', name: 'Header' },
    { selector: '.sidebar', name: 'Sidebar' },
    { selector: 'form', name: 'Forms' },
    { selector: 'table', name: 'Tables' },
    { selector: '.card', name: 'Cards' },
    { selector: 'button', name: 'Buttons' }
  ];

  try {
    // Test 1: Initial theme load and toggle on each page
    for (const page of pagesToTest) {
      await testPageTheme(page.route, page.name, browser);
    }
    
    // Test 2: Theme persistence between pages
    await testThemePersistence(pagesToTest, browser);
    
    // Test 3: System preference detection
    await testSystemPreference(browser);
    
    console.log('All tests passed!');
  } catch (error) {
    console.error(`Test failed: ${error.message}`);
  } finally {
    await browser.close();
  }
})();

// Function to test theme on a specific page
async function testPageTheme(route, pageName, browser) {
  const page = await browser.newPage();
  console.log(`Testing ${pageName} page (${route})...`);
  
  await page.goto(`http://localhost:3000${route}`);
  await page.waitForSelector('body');
  
  // Check initial theme
  const initialTheme = await page.evaluate(() => {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
  });
  
  console.log(`${pageName} initial theme: ${initialTheme}`);
  
  // Take screenshot of initial state
  await page.screenshot({ path: `${pageName.toLowerCase()}-${initialTheme}-mode.png` });
  
  // Toggle theme
  await page.waitForSelector('[aria-label*="Switch to"]');
  await page.click('[aria-label*="Switch to"]');
  await page.waitForTimeout(1000); // Wait for animation
  
  // Check toggled theme
  const toggledTheme = await page.evaluate(() => {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
  });
  
  console.log(`${pageName} theme after toggle: ${toggledTheme}`);
  
  // Take screenshot of toggled state
  await page.screenshot({ path: `${pageName.toLowerCase()}-${toggledTheme}-mode.png` });
  
  if (initialTheme === toggledTheme) {
    throw new Error(`Theme did not change after toggle on ${pageName} page`);
  }
  
  await page.close();
}

// Function to test theme persistence between pages
async function testThemePersistence(pages, browser) {
  const page = await browser.newPage();
  console.log('Testing theme persistence between pages...');
  
  // Set initial theme
  await page.goto('http://localhost:3000');
  await page.waitForSelector('[aria-label*="Switch to"]');
  
  // Get current theme
  const initialTheme = await page.evaluate(() => {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
  });
  
  // Toggle to opposite theme if needed
  const targetTheme = initialTheme === 'dark' ? 'light' : 'dark';
  if (initialTheme !== targetTheme) {
    await page.click('[aria-label*="Switch to"]');
    await page.waitForTimeout(1000);
  }
  
  // Navigate to each page and check if theme persists
  for (const pageInfo of pages) {
    console.log(`Checking theme persistence on ${pageInfo.name}...`);
    await page.goto(`http://localhost:3000${pageInfo.route}`);
    await page.waitForSelector('body');
    
    const currentTheme = await page.evaluate(() => {
      return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    });
    
    if (currentTheme !== targetTheme) {
      throw new Error(`Theme did not persist when navigating to ${pageInfo.name} page`);
    }
  }
  
  await page.close();
}

// Function to test system preference detection
async function testSystemPreference(browser) {
  const page = await browser.newPage();
  console.log('Testing system preference detection...');
  
  // Emulate light preference
  await page.emulateMediaFeatures([
    { name: 'prefers-color-scheme', value: 'light' }
  ]);
  
  await page.goto('http://localhost:3000/settings');
  await page.waitForSelector('body');
  
  // Clear localStorage to ensure we're testing system preference
  await page.evaluate(() => localStorage.removeItem('theme'));
  
  // Reload page to trigger system preference detection
  await page.reload();
  await page.waitForSelector('body');
  
  const lightTheme = await page.evaluate(() => {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
  });
  
  console.log(`Theme with light system preference: ${lightTheme}`);
  
  // Emulate dark preference
  await page.emulateMediaFeatures([
    { name: 'prefers-color-scheme', value: 'dark' }
  ]);
  
  await page.reload();
  await page.waitForSelector('body');
  
  const darkTheme = await page.evaluate(() => {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
  });
  
  console.log(`Theme with dark system preference: ${darkTheme}`);
  
  if (lightTheme === darkTheme) {
    throw new Error('System preference detection is not working correctly');
  }
  
  await page.close();
}
```

### 2. Manual Testing Checklist

#### Visual Component Testing
- [ ] Check all UI components in both themes
- [ ] Verify form elements (inputs, dropdowns, buttons) in both themes
- [ ] Test charts and visualizations in both themes
- [ ] Validate modals and dialogs in both themes
- [ ] Test toast notifications in both themes

#### Accessibility Testing
- [ ] Verify color contrast for all text elements in both themes
- [ ] Check focus indicators in both themes
- [ ] Test keyboard navigation in both themes
- [ ] Use screen reader to verify theme announcements

#### Functionality Testing
- [ ] Verify theme toggle works from all locations
- [ ] Test theme persistence across browser refresh
- [ ] Validate theme persistence in localStorage
- [ ] Test system preference detection
- [ ] Check theme loading during SSR/initial page load

### 3. Contrast Testing

We'll use the Web Content Accessibility Guidelines (WCAG) to ensure our color contrast ratios meet accessibility standards:

- Regular text (AA): 4.5:1
- Large text (AA): 3:1
- Regular text (AAA): 7:1
- Large text (AAA): 4.5:1

Tools to use:
- Chrome DevTools' Lighthouse audit
- WebAIM Contrast Checker
- Axe Accessibility browser extension

### 4. Browser Compatibility Testing

Test the theme implementation in multiple browsers:
- Chrome
- Firefox
- Safari
- Edge

### 5. Device Testing

Test on various devices to ensure responsive design works with both themes:
- Desktop
- Tablet
- Mobile

## Implementation Plan

1. Enhance the existing Puppeteer script based on the template above
2. Create a simple test harness page that shows all UI components for rapid visual testing
3. Implement the manual testing checklist 
4. Document any issues found in theme_audit_progress.md
5. Address identified issues
6. Run final verification tests

## Documentation

Document the patterns and best practices for theme implementation in the project:

1. Use CSS variables with the `--T-` prefix for theme variables
2. Use Tailwind's `dark:` modifier for theme-specific styling
3. Use the `useTheme()` hook to access the current theme in React components
4. Use conditional styling based on the current theme value
5. Test both themes during development

## Timeline

1. Week 1: Enhance Puppeteer script and create test harness
2. Week 2: Execute manual testing and document issues
3. Week 3: Address identified issues
4. Week 4: Final verification and documentation 