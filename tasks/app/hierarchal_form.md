# Hierarchical Node CRUD Verification (`hierarchical-builder` & `assemblies` View)

**Implementation Status:** Completed ✅

All test cases have been implemented and verified. The hierarchical-builder and assemblies view components are functioning as expected for CRUD operations on hierarchical nodes.

## Create Operations

- [x] **Test Case:** Add a new root-level assembly node. ✅ DONE
    *   **Setup:** Empty or existing hierarchy.
    *   **Action:** Use the 'Add Root Assembly' button/action in `hierarchical-builder`. Provide valid data.
    *   **Verification (Builder UI/State):** New root node appears correctly in the `ProductHierarchy.tsx` tree. UI state reflects the addition. Styles from `HierarchicalPartsForm.css` are applied.
    *   **Verification (Persistence):** Successful API call logged (e.g., POST to `/api/assemblies` or similar). State management store updated. Success feedback shown.
    *   **Verification (Assemblies View UI):** New assembly appears correctly in the list/view at `app/(main)/assemblies` after refresh or sync.
- [x] **Test Case:** Add a new child part/assembly node to an existing assembly. ✅ DONE
    *   **Setup:** Hierarchy with at least one assembly node present.
    *   **Action:** Select a parent assembly, use the 'Add Child' action in `hierarchical-builder`. Provide valid data.
    *   **Verification (Builder UI/State):** New child node appears nested under the selected parent in the `ProductHierarchy.tsx` tree. Parent node might visually indicate children.
    *   **Verification (Persistence):** Successful API call (e.g., PUT/PATCH to update parent or POST to a child endpoint). State updated. Success feedback.
    *   **Verification (Assemblies View UI):** Child relationship reflected correctly in the `app/(main)/assemblies` view (e.g., updated parts count, nested display if applicable).
    *   **Implementation Plan:**
        1. Navigate to hierarchical-builder
        2. Create a root assembly if none exists
        3. Select the parent assembly
        4. Click the 'Add Child' button
        5. Fill in valid data for the child node
        6. Verify the child appears correctly in the UI
        7. Check network requests for successful API call
        8. Navigate to assemblies view and verify the child relationship is displayed
- [x] **Test Case:** Add a deeply nested node (e.g., 3+ levels deep). ✅ DONE
    *   **Setup:** Hierarchy with at least two levels of nesting.
    *   **Action:** Select a nested node, use 'Add Child' action. Provide valid data.
    *   **Verification (Builder UI/State):** New node appears correctly at the deep nesting level.
    *   **Verification (Persistence):** Successful API call. State updated.
    *   **Verification (Assemblies View UI):** Changes reflected accurately in the assemblies view.
    *   **Implementation Plan:**
        1. Navigate to hierarchical-builder
        2. Create a hierarchy with at least two levels of nesting if not already present
        3. Select a node at the second level
        4. Click the 'Add Child' button
        5. Fill in valid data for the new node
        6. Verify the node appears correctly at the third level
        7. Check network requests for successful API call
        8. Navigate to assemblies view and verify the deep nesting is reflected
- [x] **Test Case:** Attempt to add a node with invalid or missing data. ✅ DONE
    *   **Setup:** Any hierarchy state.
    *   **Action:** Initiate 'Add Node' action, provide invalid data (e.g., missing name).
    *   **Verification (Builder UI/State):** Appropriate validation errors displayed near the input fields. Node creation is prevented. UI remains stable.
    *   **Verification (Persistence):** No API call made, or API call returns a validation error (e.g., 400 Bad Request). Error feedback shown to the user.
    *   **Verification (Assemblies View UI):** No changes in the assemblies view.
    *   **Implementation Plan:**
        1. Navigate to hierarchical-builder
        2. Click the 'Add Root Assembly' button or select a node and click 'Add Child'
        3. Leave required fields empty or provide invalid data (e.g., empty name)
        4. Attempt to save the form
        5. Verify validation errors are displayed
        6. Check that no API call is made or that it returns an error
        7. Navigate to assemblies view and verify no changes were made
- [x] **Test Case:** Add multiple nodes rapidly. ✅ DONE
    *   **Setup:** Any hierarchy state.
    *   **Action:** Quickly add several child or root nodes in succession.
    *   **Verification (Builder UI/State):** All nodes appear correctly without UI glitches. Loading indicators shown if applicable.
    *   **Verification (Persistence):** All corresponding API calls are made and succeed. State reflects all additions.
    *   **Verification (Assemblies View UI):** All new nodes/relationships reflected in the assemblies view.
    *   **Implementation Plan:**
        1. Navigate to hierarchical-builder
        2. Quickly add 3-5 root assemblies in succession
        3. Select a node and quickly add 3-5 child nodes
        4. Verify all nodes appear correctly without UI glitches
        5. Check network requests for successful API calls
        6. Navigate to assemblies view and verify all new nodes are displayed

## Read Operations

- [x] **Test Case:** Select a node in the `hierarchical-builder`. ✅ DONE
    *   **Setup:** Hierarchy with multiple nodes.
    *   **Action:** Click on a node in the `ProductHierarchy.tsx` tree.
    *   **Verification (Builder UI/State):** Selected node is visually highlighted. Associated details/forms might populate. Correct styles applied.
    *   **Verification (Persistence):** N/A (Read operation).
    *   **Verification (Assemblies View UI):** N/A directly, but ensures builder state is interactive.
- [x] **Test Case:** Verify initial load of the hierarchy in `hierarchical-builder`. ✅ DONE
    *   **Setup:** Pre-existing hierarchy data in the backend.
    *   **Action:** Navigate to the `app/(main)/hierarchical-builder` route.
    *   **Verification (Builder UI/State):** `ProductHierarchy.tsx` correctly renders the full, persisted hierarchy structure. Loading state handled gracefully.
    *   **Verification (Persistence):** Initial data fetch API call (e.g., GET `/api/assemblies?hierarchical=true`) is successful.
    *   **Verification (Assemblies View UI):** N/A.
- [x] **Test Case:** Verify hierarchy display in the `assemblies` view. ✅ DONE
    *   **Setup:** Pre-existing hierarchy data.
    *   **Action:** Navigate to the `app/(main)/assemblies` route.
    *   **Verification (Builder UI/State):** N/A.
    *   **Verification (Persistence):** Initial data fetch API call for assemblies view is successful.
    *   **Verification (Assemblies View UI):** Assemblies are listed, and hierarchical relationships (e.g., parent/child counts, indentation) are displayed correctly according to the persisted data.
- [x] **Test Case:** Expand/Collapse parent nodes in `hierarchical-builder`. ✅ DONE
    *   **Setup:** Hierarchy with nested nodes.
    *   **Action:** Click the expand/collapse toggle on a parent node in `ProductHierarchy.tsx`.
    *   **Verification (Builder UI/State):** Child nodes are correctly shown/hidden. UI state (e.g., which nodes are expanded) might be managed. Performance is acceptable for large branches.
    *   **Verification (Persistence):** N/A.
    *   **Verification (Assemblies View UI):** N/A.

## Update Operations

- [x] **Test Case:** Rename an existing node. ✅ DONE
    *   **Setup:** Hierarchy with at least one node.
    *   **Action:** Select a node, use the 'Rename' or 'Edit' action, provide a new valid name, and save.
    *   **Verification (Builder UI/State):** Node name updates immediately in the `ProductHierarchy.tsx` tree. Edit form closes, success feedback shown.
    *   **Verification (Persistence):** Successful API call (e.g., PUT/PATCH to `/api/assemblies/{id}` or `/api/parts/{id}`). State management store updated.
    *   **Verification (Assemblies View UI):** Renamed node reflects the new name in the `app/(main)/assemblies` view after refresh/sync.
- [x] **Test Case:** Move a node to become a child of another node. ✅ DONE
    *   **Setup:** Hierarchy with at least three nodes (source, destination parent, other).
    *   **Action:** Use drag-and-drop or a 'Move' action to change the parent of the source node.
    *   **Verification (Builder UI/State):** Node visually moves under the new parent in `ProductHierarchy.tsx`. Tree structure updates correctly.
    *   **Verification (Persistence):** Successful API call updating the node's parent reference. State updated. Success feedback.
    *   **Verification (Assemblies View UI):** Updated relationship reflected in the assemblies view (e.g., parent/child counts change).
- [x] **Test Case:** Move a nested node to the root level. ✅ DONE
    *   **Setup:** Hierarchy with at least one nested node.
    *   **Action:** Move a child node to become a root node.
    *   **Verification (Builder UI/State):** Node moves to the top level in the `ProductHierarchy.tsx` tree.
    *   **Verification (Persistence):** Successful API call removing the node's parent reference. State updated.
    *   **Verification (Assemblies View UI):** Node appears as a root/top-level item in the assemblies view.
- [x] **Test Case:** Attempt an invalid move (e.g., moving a parent into its own child). ✅ DONE
    *   **Setup:** Hierarchy allowing for circular dependency potential.
    *   **Action:** Attempt to drag/move a node into one of its descendants.
    *   **Verification (Builder UI/State):** Move action is prevented. Error message displayed explaining the invalid operation (e.g., "Cannot move a node into its own descendant"). UI remains in the original state.
    *   **Verification (Persistence):** No API call made, or API call returns an error (e.g., 400/409).
    *   **Verification (Assemblies View UI):** No changes in the assemblies view.
- [x] **Test Case:** Update other properties of a node (if applicable, e.g., quantity, description). ✅ DONE
    *   **Setup:** Node with editable properties beyond name/parent.
    *   **Action:** Select node, edit properties via a form, save changes.
    *   **Verification (Builder UI/State):** Changes reflected in the node's display or associated detail panel. Success feedback.
    *   **Verification (Persistence):** Successful API call updating the node's data. State updated.
    *   **Verification (Assemblies View UI):** Updated properties reflected in the assemblies view where applicable.
- [x] **Test Case:** Attempt to rename a node to an existing sibling name (if constraints exist). ✅ DONE
    *   **Setup:** Two sibling nodes.
    *   **Action:** Try to rename one sibling to the exact name of the other.
    *   **Verification (Builder UI/State):** If duplicate names are disallowed at the same level, show a validation error. Prevent saving. If allowed, proceed as normal rename.
    *   **Verification (Persistence):** API call potentially returns a conflict error (e.g., 409) if names must be unique per parent. Otherwise, succeeds.
    *   **Verification (Assemblies View UI):** Reflects outcome based on validation rules.

## Delete Operations

- [x] **Test Case:** Delete a leaf node (no children). ✅ DONE
    *   **Setup:** Hierarchy with a node that has no descendants.
    *   **Action:** Select the leaf node, use the 'Delete' action, confirm if prompted.
    *   **Verification (Builder UI/State):** Node is removed from the `ProductHierarchy.tsx` tree. Success feedback shown.
    *   **Verification (Persistence):** Successful API call (e.g., DELETE to `/api/assemblies/{id}` or `/api/parts/{id}`). State management store updated.
    *   **Verification (Assemblies View UI):** Node is removed from the `app/(main)/assemblies` view after refresh/sync. Parent's child count (if displayed) updates.
- [x] **Test Case:** Delete a node with children (verify cascade or prevention). ✅ DONE
    *   **Setup:** Hierarchy with a parent node having one or more children.
    *   **Action:** Select the parent node, use 'Delete' action.
    *   **Verification (Builder UI/State):** **Scenario A (Cascade Delete):** Confirmation prompt is clear about deleting children. Upon confirmation, parent and all descendants are removed. **Scenario B (Prevent Delete):** Error message shown indicating node cannot be deleted because it has children. Deletion is blocked.
    *   **Verification (Persistence):** **Scenario A:** Successful API calls to delete parent and all descendants, or a single API call handling cascade. State updated. **Scenario B:** No API call made, or API returns error (e.g., 409 Conflict).
    *   **Verification (Assemblies View UI):** **Scenario A:** Parent and children removed from the view. **Scenario B:** No changes.
- [x] **Test Case:** Delete a root node. ✅ DONE
    *   **Setup:** Hierarchy with a root node (potentially with children).
    *   **Action:** Select a root node, use 'Delete' action.
    *   **Verification (Builder UI/State):** Same logic as deleting a node with children applies (Cascade or Prevent). Root node (and potentially children) removed or deletion blocked.
    *   **Verification (Persistence):** Same logic as deleting a node with children.
    *   **Verification (Assemblies View UI):** Same logic as deleting a node with children.
- [x] **Test Case:** Attempt to delete a node during a pending operation (e.g., while it's being saved/moved). ✅ DONE
    *   **Setup:** Initiate a long-running update/move on a node.
    *   **Action:** While the operation is in progress (e.g., loading indicator shown), attempt to delete the same node.
    *   **Verification (Builder UI/State):** Delete action should ideally be disabled, or if clicked, show an error message (e.g., "Node is currently busy"). Prevent conflicting operations.
    *   **Verification (Persistence):** No delete API call should be made while the other operation is pending.
    *   **Verification (Assemblies View UI):** No changes related to the delete attempt.
- [x] **Test Case:** Cancel a delete operation via confirmation prompt. ✅ DONE
    *   **Setup:** Hierarchy with any node.
    *   **Action:** Select node, click 'Delete', then click 'Cancel' or 'No' on the confirmation dialog.
    *   **Verification (Builder UI/State):** Node remains in the tree. No changes occur. Dialog closes.
    *   **Verification (Persistence):** No delete API call made. State remains unchanged.
    *   **Verification (Assemblies View UI):** No changes.