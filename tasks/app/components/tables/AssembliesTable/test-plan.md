# AssembliesTable Component Test Plan

## Overview
This document outlines the test plan for the refactored AssembliesTable component to ensure all functionality works correctly after the client/server component separation.

## Test Scenarios

### 1. Rendering Tests
- [ ] Test with empty assemblies array (should display "No Assemblies Found" message)
- [ ] Test with populated assemblies array (should display table with data)
- [ ] Test with simple mode set to true (should hide action column)
- [ ] Test with simple mode set to false (should show action column)

### 2. Interactive Feature Tests
- [ ] Test name links (clicking should navigate to detail page)
- [ ] Test view action (clicking should navigate to detail page)
- [ ] Test edit action (clicking should navigate to edit page)
- [ ] Test delete action:
  - [ ] Test delete confirmation dialog appears
  - [ ] Test canceling delete (should not perform deletion)
  - [ ] Test confirming delete (should call API and show success toast)
  - [ ] Test error handling during delete

### 3. Visual Tests
- [ ] Verify badge styling for assembly ID
- [ ] Verify badge styling for parts count
- [ ] Verify proper alignment of all elements
- [ ] Verify responsive behavior

### 4. Edge Cases
- [ ] Test with assemblies having no parts
- [ ] Test with assemblies having long names
- [ ] Test with various assembly stages

## Test Implementation Plan

1. Manual testing of rendering
2. Manual testing of interactive features
3. Visual inspection of the component in different contexts
4. Testing edge cases with various data inputs

## Test Execution Log

Date: [Current Date]

| Test | Status | Notes |
|------|--------|-------|
| Rendering with empty array | | |
| Rendering with data | | |
| Simple mode = true | | |
| Simple mode = false | | |
| Name links navigation | | |
| View action | | |
| Edit action | | |
| Delete action - dialog | | |
| Delete action - cancel | | |
| Delete action - confirm | | |
| Delete action - error | | |
| Badge styling - IDs | | |
| Badge styling - parts | | |
| Element alignment | | |
| Responsive behavior | | |
| No parts | | |
| Long names | | |
| Various stages | | | 