# ProductsTable Refactoring Plan

## Overview
This document outlines the plan for refactoring the ProductsTable component from a single client component into separate client and server components following Next.js best practices.

## Current Implementation Analysis

The current `ProductsTable.tsx` is a client component that:
- Has the `"use client"` directive at the top
- Uses React hooks (useState)
- Handles interactive functionality (deletion with confirmation)
- Uses client navigation (useRouter)
- Uses client-side notifications (toast)
- Renders a table with Product data
- Includes conditional rendering based on the `simple` prop

## Refactoring Strategy

### 1. Directory Structure
Create a proper directory structure:
```
app/components/tables/ProductsTable/
├── ProductsTable.tsx (server component)
├── ProductsTableClient.tsx (client component)
├── types.ts (shared types)
├── index.ts (export file)
```

### 2. Types Migration
Move the interfaces to a separate `types.ts` file:
- `Product` interface
- `ProductsTableProps` interface

### 3. Component Separation

#### Server Component (ProductsTable.tsx)
- Remove "use client" directive
- Create a wrapper component that accepts the props and passes them to the client component
- Simple rendering logic only

#### Client Component (ProductsTableClient.tsx)
- Add "use client" directive
- Keep all interactive logic (useState, useRouter, handleDelete, confirmAlert)
- Keep event handlers
- Implement all UI rendering

### 4. Implementation Steps

1. **Create types.ts**
   - Extract interfaces from the current component

2. **Create ProductsTableClient.tsx**
   - Copy most of the current implementation
   - Import types from types.ts
   - Keep all interactive logic
   - Ensure it has the "use client" directive

3. **Update ProductsTable.tsx**
   - Remove "use client" directive
   - Create a simple component that forwards props to the client component
   - Import the client component and types

4. **Create index.ts**
   - Export the ProductsTable component
   - Optionally export types if needed externally

5. **Test Functionality**
   - Ensure the refactored component behaves the same as the original
   - Test all interactive features

## Code Examples

### types.ts
```typescript
export interface Product {
  _id: string;
  product_id: string;
  name: string;
  description?: string;
  main_assembly_id?: {
    _id: string;
    name: string;
    part_id: string;
  };
  components?: any[];
  // Support for older model format
  id?: string;
  currentStock?: number;
  reorderLevel?: number;
  supplierManufacturer?: string;
}

export interface ProductsTableProps {
  products: Product[];
  simple?: boolean; // For simple mode without dropdown actions
}
```

### ProductsTable.tsx (Server Component)
```typescript
import { ProductsTableProps } from './types';
import ProductsTableClient from './ProductsTableClient';

/**
 * Server component wrapper for ProductsTable
 * Delegates rendering to the client component
 */
export function ProductsTable(props: ProductsTableProps) {
  return <ProductsTableClient {...props} />;
}
```

### ProductsTableClient.tsx (Client Component)
```typescript
"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
// ... other imports

import { ProductsTableProps, Product } from './types';

/**
 * Client component implementation of ProductsTable
 * Handles all interactive logic and rendering
 */
export default function ProductsTableClient({ products, simple = false }: ProductsTableProps) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});

  // ... handleDelete and other logic

  // ... rendering code
}
```

### index.ts
```typescript
export { ProductsTable } from './ProductsTable';
export type { Product, ProductsTableProps } from './types';
```

## Benefits of Refactoring

1. **Performance Optimization**
   - Server component can be rendered on the server
   - Only interactive parts need to be handled on the client

2. **Code Organization**
   - Better separation of concerns
   - Types are isolated for better maintainability

3. **Bundle Size Reduction**
   - Client-side JavaScript is reduced
   - Only interactive code is sent to the browser

4. **Improved Type Safety**
   - Centralized type definitions
   - Consistent interface across components

## Next Steps

After refactoring ProductsTable:
1. Update imports in all files that use ProductsTable
2. Test thoroughly to ensure functionality is preserved
3. Update documentation to reflect the new component structure 