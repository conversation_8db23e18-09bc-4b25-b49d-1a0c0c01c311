# AssembliesTable Refactoring Plan

## Overview
This document outlines the plan for refactoring the AssembliesTable component from a single client component into separate client and server components following Next.js best practices.

## Current Implementation Analysis

The current `AssembliesTable.tsx` is a client component that:
- Has the `"use client"` directive at the top
- Uses React hooks (useState)
- Handles interactive functionality (deletion with confirmation)
- Uses client navigation (useRouter)
- Uses client-side notifications (toast)
- Renders a table with Assembly data
- Includes conditional rendering based on the `simple` prop

## Refactoring Strategy

### 1. Directory Structure
Create a proper directory structure:
```
app/components/tables/AssembliesTable/
├── AssembliesTable.tsx (server component)
├── AssembliesTableClient.tsx (client component)
├── types.ts (shared types)
├── index.ts (export file)
└── README.md (optional documentation)
```

### 2. Types Migration
Move the interfaces to a separate `types.ts` file:
- `Assembly` interface
- `AssembliesTableProps` interface

### 3. Component Separation

#### Server Component (AssembliesTable.tsx)
- Remove "use client" directive
- Create a wrapper component that accepts the props and passes them to the client component
- Simple rendering logic only

#### Client Component (AssembliesTableClient.tsx)
- Add "use client" directive
- Keep all interactive logic (useState, useRouter, handleDelete, confirmAlert)
- Keep event handlers
- Implement all UI rendering

### 4. Implementation Steps

1. **Create types.ts**
   - Extract interfaces from the current component

2. **Create AssembliesTableClient.tsx**
   - Copy most of the current implementation
   - Import types from types.ts
   - Keep all interactive logic
   - Ensure it has the "use client" directive

3. **Update AssembliesTable.tsx**
   - Remove "use client" directive
   - Create a simple component that forwards props to the client component
   - Import the client component and types

4. **Create index.ts**
   - Export the AssembliesTable component
   - Optionally export types if needed externally

5. **Test Functionality**
   - Ensure the refactored component behaves the same as the original
   - Test all interactive features

## Code Examples

### types.ts
```typescript
export interface Assembly {
  _id: string;
  assembly_id: string;
  name: string;
  assembly_stage?: string;
  assembly_code?: string;
  parts: {
    part_id: {
      _id: string;
      part_id: string;
      name: string;
      description?: string;
    };
    quantity_required: number;
  }[];
}

export interface AssembliesTableProps {
  assemblies: Assembly[];
  simple?: boolean; // For simple mode without dropdown actions
}
```

### AssembliesTable.tsx (Server Component)
```typescript
import { AssembliesTableProps } from './types';
import AssembliesTableClient from './AssembliesTableClient';

/**
 * Server component wrapper for AssembliesTable
 * Delegates rendering to the client component
 */
export function AssembliesTable(props: AssembliesTableProps) {
  return <AssembliesTableClient {...props} />;
}
```

### AssembliesTableClient.tsx (Client Component)
```typescript
"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
// ... other imports

import { AssembliesTableProps, Assembly } from './types';

/**
 * Client component implementation of AssembliesTable
 * Handles all interactive logic and rendering
 */
export default function AssembliesTableClient({ assemblies, simple = false }: AssembliesTableProps) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});

  // ... handleDelete and other logic

  // ... rendering code
}
```

### index.ts
```typescript
export { AssembliesTable } from './AssembliesTable';
export type { Assembly, AssembliesTableProps } from './types';
```

## Benefits of Refactoring

1. **Performance Optimization**
   - Server component can be rendered on the server
   - Only interactive parts need to be handled on the client

2. **Code Organization**
   - Better separation of concerns
   - Types are isolated for better maintainability

3. **Bundle Size Reduction**
   - Client-side JavaScript is reduced
   - Only interactive code is sent to the browser

4. **Improved Type Safety**
   - Centralized type definitions
   - Consistent interface across components

## Next Steps

After refactoring AssembliesTable:
1. Update imports in all files that use AssembliesTable
2. Apply the same pattern to ProductsTable
3. Test thoroughly to ensure functionality is preserved
4. Update documentation to reflect the new component structure 