# ProductForm Refactoring Plan

## 1. Component Structure

### Current Structure
- Single `ProductForm.tsx` file with "use client" directive
- Contains both presentational elements and client-side logic
- Heavy use of React hooks (useState, useEffect, useForm)
- Direct API calls for fetching assemblies and submitting form

### Target Structure
- Split into server and client components:
  - `ProductForm.tsx` (Server component)
  - `ProductFormClient.tsx` (Client component)
  - `types.ts` (Shared types)
  - `index.ts` (Export file for backward compatibility)

## 2. Responsibilities Separation

### Server Component (`ProductForm.tsx`)
- Import and re-export client component
- Provide any server-side data that can be fetched during SSR
- Pass props to client component
- No "use client" directive

### Client Component (`ProductFormClient.tsx`)
- Add "use client" directive
- Handle all interactive behavior
- Manage form state with React Hook Form
- Handle API interactions
- Manage UI state (loading, validation, etc.)
- Render form fields and controls

### Type Definitions (`types.ts`)
- Define `ProductFormProps` interface
- Define form value types
- Define component types and states
- Export types for both components

### Index File (`index.ts`)
- Export ProductForm as default
- Export types for external use

## 3. Implementation Steps

1. Create the directory structure:
   ```
   app/components/forms/ProductForm/
   ├── index.ts
   ├── ProductForm.tsx
   ├── ProductFormClient.tsx
   └── types.ts
   ```

2. Extract and define types in `types.ts`
   - Move interfaces and types from original file
   - Define proper prop types with JSDoc comments

3. Create client component in `ProductFormClient.tsx`
   - Add "use client" directive
   - Move all interactive logic from original file
   - Use types from types.ts
   - Implement form logic, API calls, and UI rendering

4. Create server component in `ProductForm.tsx`
   - Import client component
   - Pass through props
   - No "use client" directive

5. Create index.ts for proper exports
   - Export server component as default
   - Export types

6. Update imports in dependent files
   - Locate all files importing ProductForm
   - Update import paths if necessary

## 4. Testing Approach

1. Test server component rendering
2. Test client component form validation
3. Test API interactions (create and edit modes)
4. Ensure backward compatibility with existing code
5. Verify ProductComponentsList integration

## 5. Potential Challenges

1. Managing state between server and client components
2. Handling API interactions correctly
3. Ensuring proper typing for all components
4. Maintaining backward compatibility

## 6. Implementation Timeline

1. Type extraction and definition: 1 hour
2. Client component implementation: 2-3 hours
3. Server component creation: 30 minutes
4. Index file and exports: 15 minutes
5. Testing and validation: 2 hours

Total estimated time: 6 hours 