# FormContainer Refactoring Plan

## Current Analysis

The FormContainer component should be refactored to the server/client pattern because:

1. It uses "use client" directive
2. It uses motion from framer-motion for animations
3. It contains conditional rendering based on props
4. It handles animation states

## Refactoring Steps

### 1. Create File Structure

Create the following directory structure:
```
app/components/ui/FormContainer/
├── index.ts
├── FormContainer.tsx (Server Component)
├── FormContainerClient.tsx (Client Component)
├── types.ts
└── README.md
```

### 2. Define Types (types.ts)

Extract the FormContainerProps interface:
```typescript
/**
 * Props for the FormContainer component
 */
export interface FormContainerProps {
  /** The form title displayed in the card header */
  title: string;
  /** Optional description displayed below the title */
  description?: string;
  /** Whether the form is currently submitting/loading */
  isLoading?: boolean;
  /** Form-level error message to display */
  error?: string | null;
  /** Whether to animate the container when it mounts */
  animate?: boolean;
  /** CSS class to apply to the container */
  className?: string;
  /** Content to display in the footer section */
  footer?: React.ReactNode;
  /** Max width class for the form container - defaults to max-w-3xl */
  maxWidth?: string;
  /** Children content (the form itself) */
  children: React.ReactNode;
}
```

### 3. Implement Client Component (FormContainerClient.tsx)

1. Move the existing implementation to FormContainerClient.tsx
2. Keep the "use client" directive
3. Import the FormContainerProps from types.ts
4. Keep all animation and conditional logic

### 4. Implement Server Component (FormContainer.tsx)

1. Create a simple wrapper that delegates to the client component
2. No "use client" directive needed
3. This allows the component to be imported in server components

```typescript
import { FormContainerProps } from './types';
import FormContainerClient from './FormContainerClient';

/**
 * FormContainer provides consistent styling and behavior for all forms
 * Server component wrapper for the client component
 */
export default function FormContainer(props: FormContainerProps) {
  return <FormContainerClient {...props} />;
}
```

### 5. Create Index File (index.ts)

```typescript
// Export the server component as default
export { default } from './FormContainer';
```

### 6. Create Documentation (README.md)

Document:
- Component purpose
- Usage examples
- Props reference
- Animation features
- Integration with forms

## Implementation Benefits

1. **Better Code Organization**
   - Clear separation of client-specific code
   - Improved file structure following the established pattern

2. **Server Component Support**
   - Can be imported in server components
   - Better integration with Next.js's rendering model

3. **Type Safety**
   - Centralized type definitions
   - Consistent prop interfaces

4. **Documentation**
   - Self-documenting component structure
   - Clear usage examples

## Testing Plan

1. Test with various form implementations
2. Verify animations work properly
3. Test loading and error states
4. Ensure responsive design is maintained 