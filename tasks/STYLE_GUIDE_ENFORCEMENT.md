# Style Guide Enforcement

This document outlines recommendations for implementing style guide enforcement in the Trend IMS project to ensure code consistency and maintainability.

## ESLint Configuration

ESLint is a static code analysis tool that helps identify problematic patterns in JavaScript and TypeScript code. Here's how to set it up for the project:

### Installation

```bash
npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint-plugin-react eslint-plugin-react-hooks eslint-plugin-jsx-a11y
```

### Configuration

Create a `.eslintrc.js` file in the project root with the following configuration:

```javascript
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
    project: './tsconfig.json',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
  ],
  rules: {
    // Custom rules for the project
    'react/react-in-jsx-scope': 'off', // Not needed in Next.js
    'react/prop-types': 'off', // We use TypeScript for type checking
    '@typescript-eslint/explicit-module-boundary-types': 'off', // Too verbose for React components
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'interface',
        format: ['PascalCase'],
        prefix: ['I'],
      },
      {
        selector: 'typeAlias',
        format: ['PascalCase'],
      },
      {
        selector: 'function',
        format: ['camelCase'],
      },
      {
        selector: 'variable',
        format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
      },
    ],
    'import/order': [
      'error',
      {
        groups: [
          ['builtin', 'external'],
          'internal',
          ['parent', 'sibling', 'index'],
        ],
        pathGroups: [
          {
            pattern: 'react',
            group: 'external',
            position: 'before',
          },
          {
            pattern: 'next/**',
            group: 'external',
            position: 'before',
          },
          {
            pattern: '@/app/**',
            group: 'internal',
            position: 'after',
          },
        ],
        pathGroupsExcludedImportTypes: ['react'],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true,
        },
      },
    ],
  },
};
```

## Prettier Configuration

Prettier is a code formatter that ensures consistent code style. Here's how to set it up:

### Installation

```bash
npm install --save-dev prettier eslint-config-prettier eslint-plugin-prettier
```

### Configuration

Create a `.prettierrc.js` file in the project root with the following configuration:

```javascript
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  arrowParens: 'avoid',
  endOfLine: 'lf',
};
```

Update the `.eslintrc.js` file to include Prettier:

```javascript
module.exports = {
  // ... existing configuration
  extends: [
    // ... existing extends
    'plugin:prettier/recommended',
  ],
  // ... existing rules
};
```

## Pre-commit Hooks

Use Husky and lint-staged to enforce code style on commit:

### Installation

```bash
npm install --save-dev husky lint-staged
```

### Configuration

Create a `.husky` directory and set up the pre-commit hook:

```bash
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
```

Create a `.lintstagedrc.js` file in the project root:

```javascript
module.exports = {
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
  ],
  '*.{json,md,yml}': [
    'prettier --write',
  ],
};
```

## VS Code Integration

To make it easier for developers to follow the style guide, add VS Code settings:

Create a `.vscode/settings.json` file:

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

## Style Guide Documentation

Create a comprehensive style guide document that outlines the coding standards for the project. This should include:

1. **Naming Conventions**
   - Component files: PascalCase (e.g., `BaseCard.tsx`)
   - Utility files: camelCase (e.g., `helpers.ts`)
   - Function names: camelCase (e.g., `formatDate`)
   - Variable names: camelCase (e.g., `productData`)
   - Interface names: PascalCase (e.g., `ProductCardProps`)

2. **File Structure**
   - Component organization
   - Import order
   - Export patterns

3. **Component Patterns**
   - Props interface definitions
   - State management
   - Event handling
   - Conditional rendering

4. **Documentation Standards**
   - JSDoc comments for components, interfaces, and functions
   - README files for directories
   - Code comments for complex logic

5. **Testing Standards**
   - Test file naming and location
   - Test structure and organization
   - Mocking patterns
   - Coverage expectations

## Implementation Plan

1. **Set Up ESLint and Prettier**
   - Install dependencies
   - Create configuration files
   - Run initial lint and fix issues

2. **Set Up Pre-commit Hooks**
   - Install Husky and lint-staged
   - Configure pre-commit hooks
   - Test commit process

3. **Create VS Code Settings**
   - Create `.vscode/settings.json`
   - Share with team

4. **Document Style Guide**
   - Create comprehensive style guide document
   - Review with team
   - Iterate based on feedback

5. **Onboard Team**
   - Present style guide to team
   - Provide training on tools and processes
   - Address questions and concerns

## Conclusion

Implementing style guide enforcement will ensure code consistency and maintainability across the project. By using automated tools like ESLint, Prettier, and pre-commit hooks, we can enforce these standards without adding significant overhead to the development process.
