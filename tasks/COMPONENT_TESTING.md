# Component Testing Guide

This document provides guidelines for writing unit tests for components in the Trend IMS project.

## Testing Framework

We use the following tools for testing:

- **Jest**: Testing framework
- **React Testing Library**: For testing React components
- **Mock Service Worker (MSW)**: For mocking API requests

## Test File Structure

Test files should be placed in the same directory as the component they are testing, with a `.test.tsx` or `.spec.tsx` extension.

Example:
```
app/components/ui/
  ├── BaseCard.tsx
  └── BaseCard.test.tsx
```

## Basic Test Template

Here's a basic template for a component test:

```tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@/app/context/ThemeContext';
import ComponentName from './ComponentName';

// Mock any context providers or hooks the component uses
jest.mock('@/app/context/AppContext', () => ({
  useAppContext: () => ({
    // Mock the values and functions the component needs
    sidebarExpanded: true,
    setSidebarExpanded: jest.fn(),
    // Add other context values as needed
  }),
}));

describe('ComponentName', () => {
  // Setup function to render the component with necessary providers
  const setup = (props = {}) => {
    return render(
      <ThemeProvider>
        <ComponentName {...props} />
      </ThemeProvider>
    );
  };

  it('renders correctly', () => {
    setup();
    // Assert that the component renders expected elements
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('handles user interactions', () => {
    setup();
    // Simulate user interactions
    fireEvent.click(screen.getByRole('button', { name: 'Button Text' }));
    
    // Assert the expected outcome
    expect(screen.getByText('Result Text')).toBeInTheDocument();
  });

  it('applies correct styles based on props', () => {
    setup({ color: 'blue' });
    // Assert that the correct styles are applied
    const element = screen.getByTestId('component-element');
    expect(element).toHaveClass('text-blue-500');
  });
});
```

## Testing Patterns

### 1. Testing Rendering

Test that the component renders correctly with different props:

```tsx
it('renders with default props', () => {
  setup();
  expect(screen.getByText('Default Text')).toBeInTheDocument();
});

it('renders with custom props', () => {
  setup({ title: 'Custom Title' });
  expect(screen.getByText('Custom Title')).toBeInTheDocument();
});
```

### 2. Testing User Interactions

Test that the component responds correctly to user interactions:

```tsx
it('calls onClick when button is clicked', () => {
  const handleClick = jest.fn();
  setup({ onClick: handleClick });
  
  fireEvent.click(screen.getByRole('button'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```

### 3. Testing Conditional Rendering

Test that the component renders different content based on props or state:

```tsx
it('shows loading state when isLoading is true', () => {
  setup({ isLoading: true });
  expect(screen.getByText('Loading...')).toBeInTheDocument();
});

it('shows content when isLoading is false', () => {
  setup({ isLoading: false });
  expect(screen.getByText('Content')).toBeInTheDocument();
});
```

### 4. Testing Async Behavior

Test that the component handles async operations correctly:

```tsx
it('fetches and displays data', async () => {
  setup();
  
  // Wait for async operation to complete
  expect(await screen.findByText('Loaded Data')).toBeInTheDocument();
});
```

## Best Practices

1. **Test Behavior, Not Implementation**: Focus on testing what the component does, not how it does it.
2. **Use Accessible Queries**: Prefer queries like `getByRole`, `getByLabelText`, and `getByText` over `getByTestId`.
3. **Mock External Dependencies**: Use jest.mock to mock external dependencies like context providers and API calls.
4. **Test Edge Cases**: Test how the component behaves with empty data, error states, and loading states.
5. **Keep Tests Simple**: Each test should test one specific behavior.
6. **Use Setup Functions**: Create setup functions to reduce duplication in test code.

## Example Test for a Specific Component

Here's an example test for the `StatusCard` component:

```tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@/app/context/ThemeContext';
import StatusCard from './StatusCard';
import { Package } from 'lucide-react';

describe('StatusCard', () => {
  const mockProps = {
    title: 'Inventory Status',
    data: {
      'In Stock': 120,
      'Low Stock': 15,
      'Out of Stock': 5,
    },
    mainStat: {
      value: 140,
      label: 'Total Items',
    },
    color: 'blue' as const,
    icon: <Package data-testid="package-icon" />,
    onClick: jest.fn(),
  };

  const setup = (props = {}) => {
    return render(
      <ThemeProvider>
        <StatusCard {...mockProps} {...props} />
      </ThemeProvider>
    );
  };

  it('renders the title correctly', () => {
    setup();
    expect(screen.getByText('Inventory Status')).toBeInTheDocument();
  });

  it('renders the main stat correctly', () => {
    setup();
    expect(screen.getByText('140')).toBeInTheDocument();
    expect(screen.getByText('Total Items')).toBeInTheDocument();
  });

  it('renders all data items', () => {
    setup();
    expect(screen.getByText('In Stock')).toBeInTheDocument();
    expect(screen.getByText('120')).toBeInTheDocument();
    expect(screen.getByText('Low Stock')).toBeInTheDocument();
    expect(screen.getByText('15')).toBeInTheDocument();
    expect(screen.getByText('Out of Stock')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('renders the icon', () => {
    setup();
    expect(screen.getByTestId('package-icon')).toBeInTheDocument();
  });
});
```

## Running Tests

To run tests, use the following commands:

```bash
# Run all tests
npm test

# Run tests for a specific file
npm test -- app/components/ui/BaseCard.test.tsx

# Run tests in watch mode
npm test -- --watch
```
