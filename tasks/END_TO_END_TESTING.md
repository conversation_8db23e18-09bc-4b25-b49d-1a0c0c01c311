# End-to-End Testing Guide

This document outlines recommendations for implementing end-to-end (E2E) testing in the Trend IMS project to ensure the application works correctly from a user's perspective.

## Why End-to-End Testing?

While unit tests verify that individual components work correctly in isolation, end-to-end tests verify that the entire application works correctly as a whole. E2E tests simulate real user interactions with the application, testing critical user flows from start to finish.

## Testing Tools

We recommend using either Puppeteer or Cypress for end-to-end testing:

### Puppeteer

Puppeteer is a Node.js library that provides a high-level API to control Chrome or Chromium over the DevTools Protocol. It's maintained by Google and is well-suited for headless browser testing.

#### Installation

```bash
npm install --save-dev puppeteer jest jest-puppeteer
```

#### Configuration

Create a `jest-puppeteer.config.js` file in the project root:

```javascript
module.exports = {
  launch: {
    headless: process.env.HEADLESS !== 'false',
    slowMo: process.env.SLOWMO ? parseInt(process.env.SLOWMO) : 0,
    devtools: process.env.DEVTOOLS === 'true',
    args: ['--window-size=1920,1080'],
  },
  server: {
    command: 'npm run dev',
    port: 5174,
    launchTimeout: 60000,
  },
};
```

Update the `jest.config.js` file:

```javascript
module.exports = {
  preset: 'jest-puppeteer',
  testMatch: ['**/e2e/**/*.test.js'],
  setupFilesAfterEnv: ['./jest.setup.js'],
};
```

Create a `jest.setup.js` file:

```javascript
jest.setTimeout(30000);
```

### Cypress

Cypress is a JavaScript end-to-end testing framework designed for modern web applications. It provides a more user-friendly interface and better debugging capabilities than Puppeteer.

#### Installation

```bash
npm install --save-dev cypress
```

#### Configuration

Add a script to `package.json`:

```json
{
  "scripts": {
    "cypress:open": "cypress open",
    "cypress:run": "cypress run"
  }
}
```

Initialize Cypress:

```bash
npx cypress open
```

This will create a `cypress` directory with example tests and configuration files.

Update the `cypress.json` file:

```json
{
  "baseUrl": "http://localhost:5174",
  "viewportWidth": 1280,
  "viewportHeight": 720,
  "video": false,
  "screenshotOnRunFailure": true,
  "waitForAnimations": true,
  "defaultCommandTimeout": 10000
}
```

## Test Structure

Organize your tests by user flows or features:

### Puppeteer Example

Create a directory structure like this:

```
e2e/
  flows/
    inventory/
      add-product.test.js
      edit-product.test.js
    orders/
      create-order.test.js
      fulfill-order.test.js
  utils/
    helpers.js
    selectors.js
```

Example test file (`e2e/flows/inventory/add-product.test.js`):

```javascript
const selectors = require('../../utils/selectors');
const helpers = require('../../utils/helpers');

describe('Add Product Flow', () => {
  beforeAll(async () => {
    await page.goto('http://localhost:5174/inventory');
  });

  it('should navigate to the add product form', async () => {
    await page.click(selectors.inventory.addProductButton);
    await page.waitForSelector(selectors.inventory.productForm);
    
    const formTitle = await page.$eval(
      selectors.inventory.formTitle,
      el => el.textContent
    );
    
    expect(formTitle).toContain('Add New Product');
  });

  it('should add a new product successfully', async () => {
    const productName = `Test Product ${Date.now()}`;
    
    await page.type(selectors.inventory.nameInput, productName);
    await page.type(selectors.inventory.descriptionInput, 'This is a test product');
    await page.type(selectors.inventory.priceInput, '99.99');
    await page.type(selectors.inventory.stockInput, '10');
    
    await page.click(selectors.inventory.categoryDropdown);
    await page.click(selectors.inventory.categoryOption('Electronics'));
    
    await page.click(selectors.inventory.submitButton);
    
    // Wait for success message
    await page.waitForSelector(selectors.common.successMessage);
    
    // Verify product was added
    await page.goto('http://localhost:5174/inventory');
    const pageContent = await page.content();
    expect(pageContent).toContain(productName);
  });
});
```

### Cypress Example

Create a directory structure like this:

```
cypress/
  integration/
    inventory/
      add-product.spec.js
      edit-product.spec.js
    orders/
      create-order.spec.js
      fulfill-order.spec.js
  support/
    commands.js
    selectors.js
```

Example test file (`cypress/integration/inventory/add-product.spec.js`):

```javascript
/// <reference types="cypress" />

describe('Add Product Flow', () => {
  beforeEach(() => {
    cy.visit('/inventory');
  });

  it('should navigate to the add product form', () => {
    cy.get('[data-testid=add-product-button]').click();
    cy.get('[data-testid=product-form]').should('be.visible');
    cy.get('[data-testid=form-title]').should('contain', 'Add New Product');
  });

  it('should add a new product successfully', () => {
    const productName = `Test Product ${Date.now()}`;
    
    cy.get('[data-testid=add-product-button]').click();
    
    cy.get('[data-testid=name-input]').type(productName);
    cy.get('[data-testid=description-input]').type('This is a test product');
    cy.get('[data-testid=price-input]').type('99.99');
    cy.get('[data-testid=stock-input]').type('10');
    
    cy.get('[data-testid=category-dropdown]').click();
    cy.get('[data-testid=category-option-electronics]').click();
    
    cy.get('[data-testid=submit-button]').click();
    
    // Wait for success message
    cy.get('[data-testid=success-message]').should('be.visible');
    
    // Verify product was added
    cy.visit('/inventory');
    cy.contains(productName).should('be.visible');
  });
});
```

## Critical User Flows to Test

Focus on testing the most critical user flows in the application:

1. **Authentication**
   - User registration
   - User login
   - Password reset

2. **Inventory Management**
   - Adding a new product
   - Editing a product
   - Deleting a product
   - Searching for products
   - Filtering products by category

3. **Order Management**
   - Creating a new order
   - Editing an order
   - Fulfilling an order
   - Cancelling an order

4. **Reporting**
   - Generating inventory reports
   - Generating sales reports
   - Exporting reports

5. **User Management**
   - Adding a new user
   - Editing user permissions
   - Deactivating a user

## Best Practices

1. **Use Data Attributes for Testing**
   - Add `data-testid` attributes to elements that need to be selected in tests
   - Example: `<button data-testid="add-product-button">Add Product</button>`

2. **Isolate Tests**
   - Each test should be independent of others
   - Use before/after hooks to set up and clean up test data

3. **Mock External Services**
   - Use mock servers for external APIs
   - Intercept network requests to control responses

4. **Test Real User Flows**
   - Focus on testing complete user journeys
   - Test edge cases and error scenarios

5. **Keep Tests Maintainable**
   - Use page objects or selectors files to centralize element selectors
   - Use helper functions for common operations

## CI/CD Integration

Integrate end-to-end tests into your CI/CD pipeline:

### GitHub Actions Example

Create a `.github/workflows/e2e-tests.yml` file:

```yaml
name: End-to-End Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  cypress-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run Cypress tests
        uses: cypress-io/github-action@v2
        with:
          build: npm run build
          start: npm start
          wait-on: 'http://localhost:5174'
```

## Conclusion

Implementing end-to-end tests will help ensure that the application works correctly from a user's perspective. By focusing on critical user flows and integrating tests into the CI/CD pipeline, we can catch issues before they reach production and provide a better user experience.
