# Next.js Migration Plan

This document outlines the steps to migrate the React application to Next.js while preserving the current UI/UX.

## Current Progress

- ✅ Set up basic Next.js project structure
- ✅ Fixed environment variables: VITE_* → NEXT_PUBLIC_*
- ✅ Added "use client" directives to context providers
- ✅ Migrated components: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ThemeToggle, Sidebar
- ✅ Updated layout files with correct imports
- ✅ Added background animations to the main layout

## Next Steps

### 1. Component Migration

Components to migrate from `src/components` to `app/components`:

- [ ] ProductTable
- [ ] ProductionCapacity 
- [ ] ProductionPlanning
- [ ] SupabaseSetup
- [ ] ThemeShowcase
- [ ] CalendarComponent
- [ ] CategoryDistribution
- [ ] DatabaseStatus
- [ ] EnhancedPartForm
- [ ] ErrorDisplay
- [ ] LogisticsMap
- [ ] PartForm
- [ ] ProductCard
- [ ] ProductHierarchy
- [ ] ProductModal
- [ ] AssemblyStatus

For each component:
1. Copy the component to `app/components/`
2. Add the "use client" directive at the top
3. Update imports to use relative paths
4. Replace react-router imports with next/navigation and next/link
5. Test the component renders correctly

### 2. Route Migration

Pages to migrate from `src/pages` to Next.js App Router:

- [ ] Dashboard → app/(main)/page.tsx (in progress)
- [ ] Inventory → app/(main)/inventory/page.tsx
- [ ] Logistics → app/(main)/logistics/page.tsx
- [ ] Reports → app/(main)/reports/page.tsx
- [ ] Categories → app/(main)/categories/page.tsx
- [ ] Suppliers → app/(main)/suppliers/page.tsx
- [ ] Analytics → app/(main)/analytics/page.tsx
- [ ] PurchaseOrders → app/(main)/purchase-orders/page.tsx
- [ ] WorkOrders → app/(main)/work-orders/page.tsx
- [ ] InventoryTransactions → app/(main)/inventory-transactions/page.tsx
- [ ] UserManagement → app/(main)/user-management/page.tsx
- [ ] Warehouses → app/(main)/warehouses/page.tsx
- [x] BatchTracking → app/(main)/batch-tracking/page.tsx
- [ ] ProductImport → app/(main)/product-import/page.tsx 
- [ ] ProductDetail → app/(main)/product/[productId]/page.tsx
- [ ] HierarchicalPartEntry → app/(main)/hierarchical-part-entry/page.tsx
- [ ] Settings → app/(main)/settings/page.tsx

For each page:
1. Create the appropriate directory structure
2. Add the "use client" directive at the top
3. Migrate the component content, updating imports
4. Replace navigation logic to use Next.js APIs
5. Update form handling and data fetching if necessary

### 3. Dynamic Routes

Set up dynamic routes:
- [ ] Product detail: app/(main)/product/[productId]/page.tsx
- [ ] Purchase order detail: app/(main)/purchase-orders/[orderId]/page.tsx

### 4. API Routes

Migrate API calls to Next.js API routes:
- [ ] Create API route handlers in app/api directory
- [ ] Update client code to use these routes

### 5. Testing and Refinement

- [ ] Test navigation between pages
- [ ] Ensure data fetching works correctly
- [ ] Test form submissions
- [ ] Verify all styles are applied correctly
- [ ] Check for mobile responsiveness

### 6. Performance Optimizations

- [ ] Add image optimization with next/image
- [ ] Implement server components where appropriate
- [ ] Add metadata to pages for SEO

## Migration Checklist

For each component and page:

- [ ] Add "use client" directive
- [ ] Update imports
- [ ] Replace react-router with next/navigation
- [ ] Update Link components
- [ ] Fix any TypeScript errors
- [ ] Test functionality

## Notes

- Keep all contexts in src/contexts for now
- Update imports to use relative paths (../../src/contexts/*)
- All client components should have the "use client" directive
- The application structure follows Next.js App Router conventions 