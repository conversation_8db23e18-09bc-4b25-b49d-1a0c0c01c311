# Dark Mode Implementation Best Practices

This document outlines the best practices for implementing and maintaining dark mode in the application. It's based on our comprehensive audit of the existing codebase and serves as a guide for future development.

## Architecture

The application uses a class-based dark mode implementation with Tailwind CSS. The key components include:

1. **ThemeContext**: A React context provider that manages theme state
2. **CSS Variables**: Theme-specific CSS variables with the `--T-` prefix
3. **Tailwind Configuration**: The `darkMode: 'class'` setting in `tailwind.config.js`
4. **ThemeToggle Component**: A reusable component for toggling between themes

## CSS Variables

### Naming Convention

All theme-specific CSS variables should use the `--T-` prefix:

```css
:root {
  /* Base theme (light) */
  --T-bg-primary: #FFFFFF;
  --T-text-primary: #333333;
  /* ...other variables... */
}

.dark {
  /* Dark theme overrides */
  --T-bg-primary: #1E1E1E;
  --T-text-primary: #F0F0F0;
  /* ...other variables... */
}
```

### Key Variables

These are the core theme variables used throughout the application:

| Variable | Light Mode | Dark Mode | Usage |
|----------|------------|-----------|-------|
| `--T-bg-primary` | `#FFFFFF` | `#1E1E1E` | Main background color |
| `--T-bg-sidebar` | `#F5F5F5` | `#2D2D2D` | Sidebar background |
| `--T-bg-card` | `#FFFFFF` | `#333333` | Card backgrounds |
| `--T-text-primary` | `#333333` | `#F0F0F0` | Primary text |
| `--T-text-secondary` | `#666666` | `#A0A0A0` | Secondary text |
| `--T-border-color` | `#E5E5E5` | `#444444` | Borders |
| `--T-border-subtle` | `#F0F0F0` | `#383838` | Subtle borders |

## Tailwind Usage

### Dark Mode Classes

Use Tailwind's `dark:` modifier to apply dark-mode-specific styling:

```jsx
<div className="bg-white dark:bg-card text-gray-800 dark:text-text-primary">
  Dark-mode aware content
</div>
```

### Theme-Specific Colors

Reference CSS variables in the Tailwind config:

```js
// tailwind.config.js
module.exports = {
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Direct color mappings for use with dark: prefix
        'bg': 'var(--T-bg-primary, #1E1E1E)',
        'card': 'var(--T-bg-card, #333333)',
        'sidebar': 'var(--T-bg-sidebar, #2D2D2D)',
        'hover': 'var(--dark-hover, #3E3E3E)',
        'text-primary': 'var(--T-text-primary, #F0F0F0)',
        'text-secondary': 'var(--T-text-secondary, #A0A0A0)',
        'text-headings': 'var(--T-text-headings, #FFFFFF)',
        'border': 'var(--T-border-color, #444444)',
        'border-subtle': 'var(--T-border-subtle, #383838)',
        'focus-ring': 'var(--T-focus-ring, rgba(224, 224, 224, 0.4))',
        'hover-overlay': 'var(--T-hover-overlay, rgba(255, 255, 255, 0.05))',
        'accent-primary': 'var(--T-accent-primary, #FFFFFF)',
        'accent-active': 'var(--T-accent-active, #E0E0E0)',
      }
    }
  }
}
```

## React Components

### Theme Hook

Use the `useTheme` hook to access the current theme state in components:

```jsx
import { useTheme } from '@/app/context/ThemeContext';

const MyComponent = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div>
      Current theme: {theme}
      <button onClick={toggleTheme}>Toggle Theme</button>
    </div>
  );
};
```

### Conditional Styling

Use the theme state for conditional styling in complex cases:

```jsx
const { theme } = useTheme();

// Use conditional styling when Tailwind classes aren't enough
const dynamicStyles = {
  backgroundColor: theme === 'dark' ? 'rgba(45, 45, 45, 0.8)' : 'rgba(255, 255, 255, 0.8)',
  boxShadow: theme === 'dark' ? '0 4px 6px rgba(0, 0, 0, 0.3)' : '0 4px 6px rgba(0, 0, 0, 0.1)',
};
```

### Theme-Aware Components

Create reusable components that are aware of the current theme:

```jsx
const ThemedCard = ({ children, className, ...props }) => {
  return (
    <div
      className={cn(
        "p-4 rounded-lg border",
        "bg-white dark:bg-card",
        "border-gray-200 dark:border-border",
        "text-gray-800 dark:text-text-primary",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};
```

## Testing

### Visual Testing

Test components in both light and dark modes:

```jsx
// In your test setup
import { ThemeProvider } from '@/app/context/ThemeContext';

// Test in light mode
render(
  <ThemeProvider initialTheme="light">
    <ComponentToTest />
  </ThemeProvider>
);

// Test in dark mode
render(
  <ThemeProvider initialTheme="dark">
    <ComponentToTest />
  </ThemeProvider>
);
```

### Automated Testing

Use the Puppeteer script in `theme-testing-strategy.md` for automated testing of dark mode functionality.

## Accessibility Considerations

### Color Contrast

Ensure sufficient color contrast in both themes:

- Regular text (AA): 4.5:1
- Large text (AA): 3:1

### Focus Indicators

Make sure focus indicators are visible in both themes:

```css
:focus-visible {
  outline: 2px solid var(--T-focus-ring);
  outline-offset: 2px;
}
```

## Preferred Approach for Applying Theme Colors

When implementing dark mode styling, follow these guidelines in order of preference:

1. **Use Tailwind Utility Classes**: Always prefer the Tailwind utility classes that map to theme variables:
   ```jsx
   <div className="bg-white dark:bg-card text-gray-800 dark:text-text-primary border-gray-200 dark:border-border">
     Content with proper theme styling
   </div>
   ```

2. **Use CSS Variables in Dynamic Styles**: When Tailwind classes aren't sufficient (e.g., in charts or dynamic styles):
   ```jsx
   const dynamicStyles = {
     backgroundColor: theme === 'dark' ? 'var(--T-bg-card, #333333)' : '#FFFFFF',
     color: theme === 'dark' ? 'var(--T-text-primary, #F0F0F0)' : '#333333'
   };
   ```

3. **Avoid Direct Hex/RGB Values**: Never use hardcoded color values for theme-related styling.

## Common Pitfalls

1. **Hardcoded Colors**: Avoid hardcoded color values - use CSS variables or Tailwind classes with dark: variants
2. **Inconsistent Theme Variables**: Stick to the established CSS variable naming convention
3. **Missing Dark Variants**: Always add dark: variants when using Tailwind color classes
4. **Theme Flickering**: Handle SSR properly in ThemeContext to avoid flickering
5. **Invisible Focus States**: Ensure focus states have sufficient contrast in both themes
6. **Inconsistent Class Names**: Use the standardized class names (e.g., `dark:bg-card` instead of `dark:bg-dark-card` or `dark:bg-gray-800`)

## Implementation Checklist

When implementing a new component or page:

1. ✅ Use existing UI components that already support dark mode
2. ✅ For custom styling, use Tailwind's dark: modifier
3. ✅ For complex cases, use the useTheme() hook
4. ✅ Test the component in both light and dark modes
5. ✅ Check color contrast and accessibility in both themes
6. ✅ Ensure smooth transitions between themes

By following these best practices, we can maintain a consistent and accessible dark mode experience throughout the application.