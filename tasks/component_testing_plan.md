# Component Testing Plan

## Overview
This document outlines the testing approach for the refactored components in the Trend IMS Next.js application. All components have been refactored to follow the server/client pattern for better performance and compatibility with Next.js App Router.

## Components to Test

1. **ProductForm**
2. **PartForm**
3. **AssembliesTable**
4. **ProductsTable**
5. **HeaderRightControls**

## Testing Approach

For each component, we will perform the following tests:

### 1. Server Component Rendering
- Verify that the server component renders without errors
- Check that it correctly passes props to the client component
- Ensure proper JSX structure in the rendered output

### 2. Client Component Functionality
- Test all interactive elements (buttons, inputs, etc.)
- Verify state management works correctly
- Test event handlers and callbacks
- Ensure proper form validation (for form components)

### 3. API Integration
- Test data fetching from API endpoints
- Verify form submission and data updates
- Test error handling for API requests
- Check loading states during API operations

### 4. Component Integration
- Test interaction with child components
- Verify props passing and callback handling
- Check context provider integration (if applicable)

### 5. Backward Compatibility
- Verify that imports from the original file location work correctly
- Check that the component API hasn't changed
- Ensure no regressions in functionality

## Test Implementation

We'll use a manual testing approach combined with basic rendering tests where possible.

### Manual Testing Steps
1. Create a test instance of the component
2. Interact with the component as a user would
3. Verify expected behavior and UI updates
4. Test error states and edge cases
5. Document any issues found

## Component-Specific Tests

### ProductForm Component

#### Server Component Tests
- Verify the server component renders the client component
- Check proper props passing to the client component

#### Client Component Tests
- Test form initialization with default values
- Test form initialization with existing data (edit mode)
- Verify assembly selection dropdown populates correctly
- Test component selection and quantity management
- Verify form validation for required fields
- Test form submission in create mode
- Test form submission in edit mode
- Verify error handling and display

### PartForm Component

#### Server Component Tests
- Verify the server component renders the client component
- Check proper props passing to the client component

#### Client Component Tests
- Test form initialization with default values
- Test form initialization with existing data (edit mode)
- Verify category selection dropdown populates correctly
- Test part property fields validation
- Test form submission in create mode
- Test form submission in edit mode
- Verify error handling and display

### AssembliesTable Component

#### Server Component Tests
- Verify the server component renders the client component
- Check proper props passing to the client component

#### Client Component Tests
- Test table rendering with assemblies data
- Verify sorting functionality
- Test pagination if implemented
- Verify row actions (view, edit, delete)
- Test empty state rendering

### ProductsTable Component

#### Server Component Tests
- Verify the server component renders the client component
- Check proper props passing to the client component

#### Client Component Tests
- Test table rendering with products data
- Verify sorting functionality
- Test pagination if implemented
- Verify row actions (view, edit, delete)
- Test empty state rendering

### HeaderRightControls Component

#### Server Component Tests
- Verify the server component renders the client component
- Check proper props passing to the client component

#### Client Component Tests
- Test user dropdown or menu functionality
- Verify theme toggle if present
- Test notification display if implemented
- Verify any other interactive elements

## Test Documentation

For each component, document:
1. Test results (pass/fail)
2. Any issues discovered
3. Screenshots of component states
4. Suggestions for improvements 