# Next.js Best Practices

This document outlines the best practices for developing with Next.js in our application.

## Component Architecture

### Server vs Client Components

1. **Default to Server Components**
   - Components are Server Components by default in the App Router
   - Only add `"use client"` when necessary for client-side interactivity
   - Keep as much logic on the server as possible for better performance

2. **Client Component Pattern**
   ```jsx
   // app/components/example/index.js
   export { default } from './example'

   // app/components/example/example.js (Server Component by default)
   import ExampleClientComponent from './example-client'

   export default function Example({ children }) {
     return (
       <div>
         <ExampleClientComponent>{children}</ExampleClientComponent>
       </div>
     )
   }

   // app/components/example/example-client.js
   "use client"

   export default function ExampleClientComponent({ children }) {
     return <div>{children}</div>
   }
   ```

3. **Component Tree Optimization**
   - Move interactive Client Components down the tree
   - Keep static elements as Server Components
   - Use composition to mix Server and Client Components effectively

### Context and State Management

1. **Context Provider Implementation**
   ```jsx
   'use client'
   import { createContext } from 'react'

   export const ThemeContext = createContext({})

   export default function ThemeProvider({ children }) {
     return <ThemeContext.Provider value="dark">{children}</ThemeContext.Provider>
   }
   ```

2. **Server-Only Code**
   ```jsx
   // Server Component
   import { db } from './database'

   async function getData() {
     return db.query('SELECT * FROM users')
   }

   export default async function ServerComponent() {
     const data = await getData()
     return <>...</>
   }
   ```

## Routing and Data Fetching

### API Routes

1. **Route Handler Best Practices**
   - Use TypeScript for better type safety
   - Implement proper error handling
   - Use appropriate HTTP methods
   - Include data validation
   - Add comprehensive logging

2. **Data Revalidation**
   ```typescript
   export const revalidate = 60 // Revalidate every 60 seconds

   export async function GET() {
     const data = await fetch('https://api.example.com/data')
     const result = await data.json()
     return Response.json(result)
   }
   ```

### Middleware

1. **URL Handling**
   ```typescript
   import { NextResponse } from 'next/server'
   import type { NextRequest } from 'next/server'

   export function middleware(request: NextRequest) {
     const url = request.nextUrl.clone()
     url.pathname = '/new-path'
     return NextResponse.rewrite(url)
   }
   ```

2. **Authentication**
   ```typescript
   export function middleware(request: NextRequest) {
     if (isAuthValid(request)) {
       return NextResponse.next()
     }

     request.nextUrl.searchParams.set('from', request.nextUrl.pathname)
     request.nextUrl.pathname = '/login'
     return NextResponse.redirect(request.nextUrl)
   }
   ```

## Performance Optimization

### Dynamic Imports

1. **Client-Only Components**
   ```jsx
   import dynamic from 'next/dynamic'

   const ClientOnlyComponent = dynamic(() => import('./component'), {
     ssr: false,
   })
   ```

2. **Optimized Icon Imports**
   ```jsx
   // Instead of this:
   import { Icon1, Icon2 } from 'react-icons/md'

   // Do this:
   import Icon1 from 'react-icons/md/Icon1'
   import Icon2 from 'react-icons/md/Icon2'
   ```

### Build Optimization

1. **Webpack Configuration**
   ```javascript
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     webpack: (config) => {
       config.resolve.alias = {
         ...config.resolve.alias,
         // your aliases
       }
       return config
     },
   }
   ```

2. **Tailwind CSS Optimization**
   ```javascript
   module.exports = {
     content: [
       './src/**/*.{js,ts,jsx,tsx}', // Good
       // Avoid overly broad patterns
     ],
   }
   ```

## Testing and Development

### Environment Setup

1. **Custom Environment Variables**
   ```bash
   # Use APP_ENV instead of NODE_ENV for custom environments
   APP_ENV=staging
   ```

2. **Testing Configuration**
   ```json
   {
     "scripts": {
       "dev": "next dev",
       "build": "next build",
       "start": "next start",
       "lint": "next lint",
       "cypress:open": "cypress open"
     }
   }
   ```

## Security Best Practices

1. **Authentication and Authorization**
   - Implement proper role-based access control
   - Use secure session management
   - Validate user permissions on both client and server
   - Handle authentication errors gracefully

2. **Data Validation**
   - Validate all user inputs
   - Implement proper error handling
   - Use TypeScript for type safety
   - Add request validation in API routes

3. **API Security**
   - Implement rate limiting
   - Use proper CORS configuration
   - Validate request origins
   - Handle sensitive data properly

## Error Handling

1. **Client-Side Errors**
   - Implement error boundaries
   - Add proper error logging
   - Show user-friendly error messages
   - Handle network errors gracefully

2. **Server-Side Errors**
   - Log errors comprehensively
   - Return appropriate status codes
   - Provide helpful error messages
   - Handle database errors properly

## Documentation

1. **Code Documentation**
   - Add JSDoc comments for functions
   - Document component props
   - Include usage examples
   - Keep documentation up-to-date

2. **API Documentation**
   - Document all API endpoints
   - Include request/response examples
   - Document error responses
   - Keep API documentation current 