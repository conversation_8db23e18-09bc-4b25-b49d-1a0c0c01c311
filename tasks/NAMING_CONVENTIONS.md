# Naming Conventions

This document outlines the naming conventions to be followed in the Trend IMS project to ensure consistency and maintainability.

## Files and Directories

### Components

- **Component Files**: Use PascalCase for component files (e.g., `ProductCard.tsx`, `Header.tsx`)
- **Component Directories**: Use camelCase for component directories (e.g., `ui`, `layout`, `forms`)
- **Test Files**: Append `.test` or `.spec` to the component name (e.g., `ProductCard.test.tsx`)

### Utilities and Services

- **Utility Files**: Use camelCase for utility files (e.g., `helpers.ts`, `formatters.ts`)
- **Service Files**: Use camelCase for service files (e.g., `supabase.ts`, `authService.ts`)

### Types and Interfaces

- **Type Files**: Use camelCase for type files (e.g., `index.ts`, `product.ts`)
- **Type Directories**: Use camelCase for type directories (e.g., `inventory`, `orders`, `user`)

## Code Conventions

### Components

- **Component Names**: Use PascalCase for component names (e.g., `ProductCard`, `Header`)
- **Props Interfaces**: Name as `ComponentNameProps` (e.g., `ProductCardProps`, `HeaderProps`)
- **State Interfaces**: Name as `ComponentNameState` (e.g., `ProductCardState`, `HeaderState`)

### Functions

- **Function Names**: Use camelCase for function names (e.g., `fetchProducts`, `formatDate`)
- **Event Handlers**: Prefix with `handle` or `on` (e.g., `handleSubmit`, `onProductSelect`)
- **Async Functions**: Consider prefixing with `fetch`, `get`, `update`, etc. (e.g., `fetchUserData`, `updateProduct`)

### Variables

- **Variables**: Use camelCase for variable names (e.g., `productList`, `currentUser`)
- **Constants**: Use UPPER_SNAKE_CASE for constants (e.g., `MAX_ITEMS`, `API_URL`)
- **Boolean Variables**: Prefix with `is`, `has`, `should`, etc. (e.g., `isLoading`, `hasError`)

### Types and Interfaces

- **Interfaces**: Use PascalCase for interfaces (e.g., `Product`, `User`)
- **Type Aliases**: Use PascalCase for type aliases (e.g., `OrderStatus`, `UserRole`)
- **Enums**: Use PascalCase for enum names and UPPER_SNAKE_CASE for enum values (e.g., `enum OrderStatus { PENDING, SHIPPED }`)

## CSS and Styling

- **CSS Classes**: Use kebab-case for CSS classes (e.g., `product-card`, `header-title`)
- **CSS Variables**: Use kebab-case for CSS variables (e.g., `--primary-color`, `--font-size-large`)
- **Tailwind Classes**: Follow Tailwind CSS conventions

## Database

- **Table Names**: Use snake_case for table names (e.g., `products`, `purchase_orders`)
- **Column Names**: Use snake_case for column names (e.g., `product_id`, `created_at`)

## Documentation

- **Documentation Files**: Use UPPER_SNAKE_CASE for documentation files (e.g., `README.md`, `NAMING_CONVENTIONS.md`)
- **JSDoc Comments**: Use JSDoc format for code documentation

## Examples

### Component Example

```tsx
// ProductCard.tsx
interface ProductCardProps {
  product: Product;
  onSelect: (id: string) => void;
  isSelected: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onSelect, isSelected }) => {
  const handleClick = () => {
    onSelect(product.id);
  };
  
  return (
    <div className="product-card" onClick={handleClick}>
      {/* Component content */}
    </div>
  );
};
```

### Utility Function Example

```ts
// helpers.ts
/**
 * Format a date to a readable string
 * @param date The date to format
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string): string => {
  if (!date) return '';
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};
```
