# Button Component Refactoring Plan

## Current Analysis

The Button component should be refactored to the server/client pattern because:

1. It uses "use client" directive
2. It uses client-side features like React.forwardRef
3. It leverages the Slot component from @radix-ui/react-slot
4. It handles dynamic class generation through class-variance-authority

## Refactoring Steps

### 1. Create File Structure

Create the following directory structure:
```
app/components/ui/Button/
├── index.ts
├── Button.tsx (Server Component)
├── ButtonClient.tsx (Client Component)
├── types.ts
└── README.md
```

### 2. Define Types (types.ts)

Extract the button-related types and variants:
```typescript
import { type VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";

/**
 * Button variants using class-variance-authority
 */
export const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 dark:bg-accent dark:text-accent-foreground dark:hover:bg-accent/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground dark:border-border dark:bg-background dark:hover:bg-dark-hover dark:hover:text-accent",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 dark:bg-dark-element dark:text-dark-text-primary dark:hover:bg-dark-hover",
        ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-dark-hover dark:hover:text-accent",
        link: "text-primary underline-offset-4 hover:underline dark:text-accent",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

/**
 * Props for the Button component
 */
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}
```

### 3. Implement Client Component (ButtonClient.tsx)

Move the existing implementation to ButtonClient.tsx:
```typescript
"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cn } from "@/app/lib/utils";
import { ButtonProps, buttonVariants } from "./types";

/**
 * Client component for Button that handles forwarded refs and dynamic styling
 */
const ButtonClient = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
ButtonClient.displayName = "ButtonClient";

export default ButtonClient;
```

### 4. Implement Server Component (Button.tsx)

Create a wrapper that re-exports the client component:
```typescript
import { ButtonProps } from './types';
import ButtonClient from './ButtonClient';

/**
 * Button component with various styles and sizes
 * 
 * Server component that delegates to client component since buttons
 * require client-side features like ref forwarding.
 */
function Button(props: ButtonProps) {
  return <ButtonClient {...props} />;
}

Button.displayName = "Button";

export { Button };
```

### 5. Create Index File (index.ts)

```typescript
// Export both button and variants
export { Button } from './Button';
export { buttonVariants } from './types';
```

### 6. Create Documentation (README.md)

Document:
- Button variants and sizes
- Usage examples
- Slot pattern for composition
- Dark mode support

## Implementation Benefits

1. **Better Organization**
   - Separation of variants definition from component implementation
   - Clear component responsibilities

2. **Improved Type Safety**
   - Centralized type definitions
   - Better IntelliSense support

3. **Maintainability**
   - Easier to extend or modify variants
   - Clean component structure

## Testing Plan

1. Verify all button variants render correctly
2. Test with different sizes
3. Test asChild functionality
4. Confirm dark mode styling works 