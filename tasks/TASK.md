# TASK.md - Investigate Stock/Reorder Level Update Issue

1.  [x] **Analyze Frontend Display:** Checked `ProductsTableClient.tsx` and `ProductTable.tsx`. They display `currentStock` and `reorderLevel`.
2.  [x] **Analyze Frontend Data Fetching:** Checked `AppContext.tsx`. It uses `fetchData` which calls `getAllParts` from `app/lib/data/parts.ts`. This seems to hit `GET /api/parts`.
3.  [x] **Analyze API Endpoint (`GET /api/parts`)**: Checked `app/api/parts/route.ts`. It correctly projects `current_stock` and `reorder_level`.
4.  [x] **Analyze API Endpoint (`PUT /api/parts/[id]`)**: Checked `app/api/parts/[id]/route.ts`. It receives data and passes it to `mongodbService.updatePart`.
5.  [x] **Analyze Part Model (`app/models/part.model.ts`)**: Checked schema. Defines `current_stock` and `reorder_level` (snake_case, required, default 0). Also has redundant `inventoryLevel`.
6.  [x] **Analyze DB Service (`app/services/mongodb.ts`)**:
    *   [x] Checked `updatePart`: Updates all fields passed in `partData`. Correctly handles `current_stock`/`reorder_level` *if* provided.
    *   [x] Checked `getPart`: Fetches full document.
    *   [x] Identified `fetchParts` and `searchParts` functions that incorrectly project `inventoryLevel` instead of `current_stock`/`reorder_level`.
7.  [x] **Investigate Update Mechanism:**
    *   [x] Found frontend component for parts: `PartFormClient.tsx`.
    *   [x] Inferred API call: Likely `PUT /api/parts/[id]`.
    *   [x] Verified data payload: Form schema (`partFormSchema`) uses `current_stock` and `reorder_level`. Data sent *should* be correct.
8.  [x] **Investigate `inventoryLevel` Usage:** Found uses in model definition, indexes, and incorrect service function projections (`fetchParts`/`searchParts`). No active update logic found yet.
9.  [x] **Investigate `fetchParts`/`searchParts` Usage:** Found no usages of these service functions. Likely dead code or unused in relevant data paths.
10. [ ] **Propose Solution:** Based on findings, suggest code changes to fix the update issue and resolve field inconsistencies. 