# Sentry MCP (Monitoring Control Panel)

## Overview

This directory contains integration files for Sentry error monitoring in the Trend Inventory Management System (IMS). Sentry MCP provides centralized error tracking, performance monitoring, and debugging capabilities for both client and server-side components.

## Features

- Real-time error tracking and reporting
- Performance monitoring for critical operations
- User context tracking for error attribution
- Integration with Next.js application framework
- Custom event and breadcrumb tracking
- Deployment tracking and release management

## Configuration

Sentry integration requires the following environment variables:

```
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
SENTRY_AUTH_TOKEN=your-auth-token (for releases and source maps)
NEXT_PUBLIC_ENABLE_SENTRY_DEV=true|false (optional, enables Sentry in development)
```

## Integration with Trend IMS

The Sentry integration is configured in the following files:

- `sentry.edge.config.ts` - Edge runtime configuration
- `sentry.server.config.ts` - Server-side configuration
- `instrumentation.ts` - OpenTelemetry instrumentation
- `instrumentation-client.ts` - Client-side instrumentation

## Development

During development, Sentry is disabled by default to avoid unnecessary noise. To enable Sentry in development mode, set `NEXT_PUBLIC_ENABLE_SENTRY_DEV=true` in your `.env` file.

### Testing Sentry Integration

To test that Sentry is properly integrated:

1. Ensure Sentry environment variables are configured
2. Run the application in development or production mode
3. Trigger a test error (e.g., `throw new Error('Test Sentry Error')`)
4. Verify the error appears in your Sentry dashboard

## Troubleshooting

Common issues:

- **No errors reported**: Check that your DSN is correct and Sentry is enabled for your environment
- **Missing source maps**: Ensure `SENTRY_AUTH_TOKEN` is set for production builds
- **Performance issues**: Consider sampling transactions to reduce overhead
- **High error volume**: Use Sentry's ignore options to filter out noise

## Resources

- [Sentry JavaScript SDK Documentation](https://docs.sentry.io/platforms/javascript/)
- [Next.js Sentry Integration Guide](https://docs.sentry.io/platforms/javascript/guides/nextjs/)