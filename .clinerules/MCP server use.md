# Rules for Using MCP Servers (Plan & Act Modes)

This document outlines the rules for selecting the appropriate Model Context Protocol (MCP) server based on the task at hand, considering **Plan** and **Act** modes. The CLI should use these guidelines to invoke the correct server.

**Plan Mode:** Focuses on understanding the request, breaking down complex tasks, and generating a step-by-step strategy.
**Act Mode:** Focuses on executing the specific steps defined in the plan, interacting with tools, code, and external systems.

---

## Servers Primarily for Plan Mode

### 1. Sequential Thinking Server (`github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking`)

* **Mode:** Plan
* **Capabilities:** `sequentialthinking`
* **When to Use:**
    * **Core Planning:** Use this as the primary server during **Plan mode** to break down complex tasks or problems into a logical sequence of executable actions (which will be handled in Act mode).
    * **Strategy Generation:** Generate high-level strategies or workflows before execution begins.
* **Example Trigger:** "Outline the steps to refactor the authentication module." (This generates the plan).

---

## Servers Primarily for Act Mode

These servers are typically invoked during **Act mode** to execute the steps defined by the planning phase.

### 2. Upstash Context Server (`github.com/upstash/context7-mcp`)

* **Mode:** Act
* **Capabilities:** `resolve-library-id`, `get-library-docs`
* **When to Use:**
    * **Execute: Resolve Library:** When a step in the plan requires identifying a code library.
    * **Execute: Fetch Docs:** When a step requires retrieving documentation for a library/function.
* **Example Trigger (Act):** "Find docs for `express.Router()`." (Executing a step from the plan).

### 3. Puppeteer Server (`github.com/modelcontextprotocol/servers/tree/main/src/puppeteer`)

* **Mode:** Act
* **Capabilities:** `puppeteer_screenshot`, `puppeteer_click`, `puppeteer_fill`, `puppeteer_select`, `puppeteer_hover`, `puppeteer_evaluate`, `puppeteer_navigate`
* **When to Use:**
    * **Execute: Browser Actions:** When a step in the plan requires interacting with a web page (navigating, clicking, filling forms, scraping, taking screenshots).
* **Example Trigger (Act):** "Navigate to `http://localhost:3000` and click the login button." (Executing a step from the plan).

### 4. Memory Server (`github.com/modelcontextprotocol/servers/tree/main/src/memory`)

* **Mode:** Act (Can support Plan by providing context)
* **Capabilities:** `create_entities`, `create_relations`, `read_graph`, `search_nodes`, `open_nodes`
* **When to Use:**
    * **Execute: Store Context:** When a step requires storing facts, relationships, or intermediate results (e.g., "Remember the variable name `userToken`").
    * **Execute: Retrieve Context:** When a step requires recalling previously stored information to proceed.
    * *(Plan Support):* Can be queried during planning to retrieve relevant past context or project structure.
* **Example Trigger (Act):** "Store the relationship: 'Auth Service' depends on 'User Database'." (Executing a step).

### 5. AgentDeskAI Browser Tools Server (`github.com/AgentDeskAI/browser-tools-mcp`)

* **Mode:** Act
* **Capabilities:** `getConsoleLogs`, `getConsoleErrors`, `getNetworkErrors`, `getNetworkLogs`, `takeScreenshot`, `getSelectedElement`, `wipeLogs`, `runAccessibilityAudit`, `runPerformanceAudit`, `runSEOAudit`, `runNextJSAudit`, `runDebuggerMode`, `runAuditMode`, `runBestPracticesAudit`
* **When to Use:**
    * **Execute: Debug Web:** When a step involves checking browser logs or errors.
    * **Execute: Audit Web:** When a step requires running performance, SEO, accessibility, or other audits.
    * **Execute: Inspect Web:** When needing to analyze specific elements or states in the browser during execution.
* **Example Trigger (Act):** "Run a performance audit on the current page." (Executing a step).

### 6. Sentry Server (`github.com/modelcontextprotocol/servers/tree/main/src/sentry`)

* **Mode:** Act
* **Capabilities:** `get_sentry_issue`
* **When to Use:**
    * **Execute: Fetch Error Details:** When a step in the plan requires getting specific information about an issue from Sentry.
* **Example Trigger (Act):** "Get details for Sentry issue `PROJECT-XYZ-123`." (Executing a step).

---

**General Guidance:**

* Use the `Sequential Thinking` server first (Plan mode) to generate a clear plan of action.
* Execute the steps in the plan using the appropriate "Act" mode servers.
* Use the `Memory` server as needed during the "Act" phase to store and recall information relevant to the execution flow.

* don't use write_to_file as it is taking too much of the context