{"env": {"node": true, "es2022": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": ["./tsconfig.json", "./tsconfig.test.json"]}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/explicit-function-return-type": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": {"arguments": false}}], "no-console": ["error", {"allow": ["warn", "error"]}], "@typescript-eslint/require-await": "warn"}, "ignorePatterns": ["build/", "coverage/", "*.js"]}