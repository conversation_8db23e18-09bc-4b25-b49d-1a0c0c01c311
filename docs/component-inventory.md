# Trend_IMS Component Inventory

## Overview
This document provides a comprehensive inventory of all React components in the Trend_IMS application, documenting their purpose, props, dependencies, and identifying opportunities for consolidation.

**Generated:** 2025-01-03  
**Status:** Phase 1 - Component Audit Complete

## Component Categories

### 1. Form Components (`app/components/forms/`)

#### Button Components
- **Location**: `app/components/forms/Button/`
- **Pattern**: Server/Client delegation
- **Files**: 
  - `Button.tsx` (Server component)
  - `ButtonClient.tsx` (Client component with animations)
  - `types.ts` (TypeScript definitions)
- **Props**: Extensive variant system using CVA
- **Variants**: default, destructive, outline, secondary, ghost, link, success, warning, info
- **Dependencies**: Framer Motion, CVA, Tailwind
- **Usage**: Widely used across application
- **Issues**: None identified - well-structured

#### Input Components
- **Location**: `app/components/forms/Input/`
- **Pattern**: Server/Client delegation
- **Files**: 
  - `Input.tsx` (Server component)
  - `InputClient.tsx` (Client component)
  - `types.ts`
- **Props**: Standard input props with theme integration
- **Dependencies**: React Hook Form compatible
- **Usage**: Forms throughout application
- **Issues**: None identified

#### Form System
- **Location**: `app/components/forms/Form/`
- **Pattern**: React Hook Form integration
- **Files**: 
  - `Form.tsx` (Server wrapper)
  - `FormClient.tsx` (Client implementation)
- **Components**: Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage
- **Dependencies**: React Hook Form, Zod validation
- **Usage**: All forms in application
- **Issues**: Inconsistent validation patterns across different forms

#### Select Components
- **Location**: `app/components/forms/Select/`
- **Components**: Select, SelectContent, SelectItem, SelectTrigger, SelectValue
- **Dependencies**: Radix UI Select
- **Usage**: Dropdown selections
- **Issues**: None identified

#### Specialized Form Components
- **AssemblyForm**: Complex form for assembly creation/editing
- **PartForm**: Part creation/editing form
- **ProductForm**: Product management form
- **EnhancedPartForm**: Enhanced version of PartForm
- **UnifiedAssemblyForm**: Consolidated assembly form
- **Issues**: Multiple similar form components suggest consolidation opportunity

### 2. Layout Components (`app/components/layout/`)

#### Card Components - **CONSOLIDATION OPPORTUNITY**
- **BaseCard**: `app/components/layout/cards/BaseCard/`
- **ActionCard**: `app/components/layout/cards/ActionCard/`
- **StatusCard**: `app/components/layout/cards/StatusCard/`
- **Card System**: `app/components/layout/cards/card.tsx` (Unified system)
- **Issue**: Multiple card implementations with overlapping functionality
- **Recommendation**: Consolidate into single Card component with variants

#### Container Components
- **Location**: `app/components/layout/Container/`
- **Pattern**: Server/Client separation
- **Files**: 
  - `Container.tsx` (Server - no animations)
  - `ContainerClient.tsx` (Client - with animations)
- **Props**: maxWidth, padding, className, centered
- **Usage**: Layout wrapper throughout application
- **Issues**: None identified - good separation

#### Sidebar Component
- **Location**: `app/components/layout/Sidebar/`
- **Pattern**: Server/Client delegation
- **Files**: 
  - `Sidebar.tsx` (Server wrapper)
  - `SidebarClient.tsx` (Complex client implementation)
  - `types.ts`
- **Features**: Collapsible sections, animations, categorized navigation
- **Dependencies**: Framer Motion, Lucide icons
- **Issues**: Complex but well-structured

#### Calendar Components
- **Calendar**: Basic calendar component
- **CalendarComponent**: Enhanced calendar
- **DatePicker**: Date selection component
- **Dependencies**: React Day Picker
- **Issues**: None identified

### 3. Data Display Components (`app/components/data-display/`)

#### Badge System
- **Location**: `app/components/data-display/badge.tsx`
- **Pattern**: CVA-based variants
- **Variants**: default, secondary, destructive, outline, success, warning
- **Dependencies**: CVA, Tailwind
- **Usage**: Status indicators throughout application
- **Issues**: None identified - well-implemented

#### Alert System
- **Components**: Alert, AlertDescription, AlertTitle
- **Usage**: Error and notification display
- **Issues**: None identified

### 4. Table Components (`app/components/tables/`) - **MAJOR CONSOLIDATION OPPORTUNITY**

#### Current Table Implementations
- **ProductsTable**: `app/components/tables/ProductsTable/`
  - Complex table with server/client delegation
  - Features: Actions, dropdowns, data management
  - Mobile responsiveness: **POOR**
- **AssembliesTable**: `app/components/tables/AssembliesTable/`
  - Assembly-specific table implementation
  - Features: Expandable rows, status indicators
  - Mobile responsiveness: **POOR**
- **InventoryTable**: Specialized inventory display
- **Issues**: 
  - Multiple similar table implementations
  - Poor mobile responsiveness across all tables
  - Inconsistent pagination patterns
  - No unified data table component

### 5. Navigation Components (`app/components/navigation/`)

#### Tab System
- **Components**: Tabs, TabsContent, TabsList, TabsTrigger
- **Dependencies**: Radix UI Tabs
- **Usage**: Content organization
- **Issues**: None identified

#### Breadcrumb System
- **Components**: Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator
- **Usage**: Navigation hierarchy
- **Issues**: Inconsistent implementation across pages

#### Navigation Buttons
- **NewAssemblyButton**: Specialized navigation button
- **Issues**: Could be generalized

### 6. Status Components (`app/components/status/`)

#### Status Badges
- **ProductStatusBadge**: Product-specific status display
- **AssemblyStatusBadge**: Assembly status display
- **PartsCountBadge**: Parts count indicator
- **Dependencies**: Tooltip integration, theme-aware styling
- **Issues**: Multiple similar badge components

### 7. Accessibility Components (`app/components/accessibility/`)

#### Comprehensive Accessibility Library
- **AccessibleButton**: Enhanced button with accessibility features
- **AccessibleLink**: Accessible link component
- **AccessibleInput**: Enhanced input with accessibility
- **AccessibleTextarea**: Accessible textarea
- **AccessibleSelect**: Accessible select component
- **AccessibleCheckbox**: Enhanced checkbox
- **AccessibleFormField**: Form field wrapper
- **SkipLink**: Skip navigation link
- **VisuallyHidden**: Screen reader only content
- **LiveRegion**: Dynamic content announcements
- **Strengths**: Comprehensive accessibility support
- **Issues**: May not be consistently used across application

### 8. Theme Components (`app/components/theme/`)

#### Theme System
- **ThemeToggle**: Light/dark mode toggle
- **Features**: Animation, system preference detection
- **Dependencies**: Framer Motion
- **Issues**: None identified

### 9. Feature Components (`app/components/features/`)

#### Specialized Components
- **AssemblyStatus**: Assembly status display
- **ProductCard**: Product display card
- **SentryTest**: Error monitoring test
- **SentryMonitor**: Error monitoring
- **Issues**: None identified

**Note**: BomViewer has been migrated to BomViewerModal in the modals directory for improved UX

### 10. Search Components (`app/components/search/`)

#### Search Functionality
- **PartSearch**: Advanced part search
- **PartSearchSimple**: Simplified part search
- **Issues**: Two similar search components suggest consolidation opportunity

## Summary of Issues Identified

### High Priority - Consolidation Opportunities

1. **Card Components**: 4 different card implementations
2. **Table Components**: Multiple table implementations with poor mobile support
3. **Form Components**: Multiple similar form components
4. **Status Badge Components**: Multiple similar badge implementations
5. **Search Components**: Duplicate search functionality

### High Priority - Mobile Responsiveness Issues

1. **All Table Components**: Poor mobile responsiveness
2. **Navigation**: Limited mobile-first patterns
3. **Touch Targets**: Need verification of 44px minimum size

### High Priority - Accessibility Gaps

1. **Inconsistent Usage**: Accessibility components not used consistently
2. **Missing ARIA Labels**: Some interactive elements lack proper labels
3. **Keyboard Navigation**: Complex components may have navigation issues

## Recommendations

### Immediate Actions (Phase 1)

1. **Create Unified Card Component**: Consolidate BaseCard, ActionCard, StatusCard
2. **Create Responsive DataTable**: Replace all table implementations
3. **Implement Mobile-First Navigation**: Enhance sidebar and navigation
4. **Standardize Form Validation**: Create unified validation system
5. **Audit Accessibility**: Ensure consistent use of accessibility components

### Component Consolidation Targets

- **Cards**: 4 → 1 unified component
- **Tables**: 3+ → 1 responsive component
- **Forms**: 5+ → Standardized pattern
- **Status Badges**: 3+ → 1 flexible component
- **Search**: 2 → 1 unified component

**Estimated Reduction**: 30% fewer components through consolidation
