# DataTable Component Documentation

## Overview

The DataTable component is a modern, responsive table component built with TanStack Table that provides a unified solution for displaying tabular data across the Trend_IMS application. It replaces the existing ProductsTable, AssembliesTable, and InventoryTable components with a single, feature-rich implementation.

## Key Features

### ✅ Mobile-First Responsive Design
- **Card View on Mobile**: Automatically switches to card layout on small screens
- **Horizontal Scroll Fallback**: Optional horizontal scrolling for complex tables
- **Priority-Based Column Display**: Show most important columns first on mobile
- **Custom Mobile Renderers**: Specialized rendering for mobile card view

### ✅ Full Accessibility Support (WCAG 2.1 AA)
- **Proper Table Structure**: Semantic HTML with table headers and scope attributes
- **Screen Reader Support**: ARIA labels and live regions for dynamic content
- **Keyboard Navigation**: Full keyboard support for all interactive elements
- **Table Captions**: Descriptive captions for screen readers

### ✅ Advanced Table Features
- **Sorting**: Multi-column sorting with visual indicators
- **Filtering**: Column-specific and global search filtering
- **Pagination**: Configurable page sizes with navigation controls
- **Column Visibility**: Toggle column visibility with dropdown menu
- **Row Selection**: Optional multi-row selection with checkboxes

### ✅ Performance Optimized
- **Virtual Scrolling**: Efficient rendering for large datasets
- **Memoized Calculations**: Optimized re-renders with React.useMemo
- **Server-Side Support**: Compatible with server-side pagination and filtering

## Installation

The DataTable component is already installed and configured. TanStack Table dependency has been added:

```bash
npm install @tanstack/react-table
```

## Basic Usage

```tsx
import { DataTable, DataTableColumn } from '@/app/components/data-display/data-table';

// Define your data type
interface Product {
  id: string;
  name: string;
  price: number;
  status: string;
}

// Define columns
const columns: DataTableColumn<Product>[] = [
  {
    id: 'name',
    header: 'Product Name',
    accessorKey: 'name',
    mobilePriority: 1, // Show first on mobile
  },
  {
    id: 'price',
    header: 'Price',
    accessorKey: 'price',
    cell: ({ row }) => `$${row.original.price.toFixed(2)}`,
    hideOnMobile: true, // Hide on mobile devices
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    mobilePriority: 2,
    cell: ({ row }) => (
      <Badge variant={row.original.status === 'active' ? 'default' : 'secondary'}>
        {row.original.status}
      </Badge>
    ),
  },
];

// Use the component
function ProductTable({ products }: { products: Product[] }) {
  return (
    <DataTable
      data={products}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      caption="Product inventory table"
    />
  );
}
```

## Advanced Configuration

### Mobile Responsiveness

```tsx
const columns: DataTableColumn<Product>[] = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    mobilePriority: 1, // Higher priority = shown first on mobile
    mobileRender: (value, row) => (
      <div>
        <div className="font-semibold">{value}</div>
        <div className="text-sm text-muted-foreground">{row.description}</div>
      </div>
    ),
  },
  {
    id: 'details',
    header: 'Details',
    accessorKey: 'description',
    hideOnMobile: true, // Hidden on mobile, shown in mobileRender above
  },
];
```

### Custom Row Actions

```tsx
const renderRowActions = (item: Product) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="ghost" className="h-8 w-8 p-0">
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem onClick={() => onEdit(item)}>
        <Edit className="mr-2 h-4 w-4" />
        Edit
      </DropdownMenuItem>
      <DropdownMenuItem onClick={() => onDelete(item)}>
        <Trash2 className="mr-2 h-4 w-4" />
        Delete
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
);

<DataTable
  data={products}
  columns={columns}
  renderRowActions={renderRowActions}
  onRowClick={(item) => console.log('Row clicked:', item)}
/>
```

### Loading and Error States

```tsx
<DataTable
  data={products}
  columns={columns}
  isLoading={isLoading}
  loadingRows={5}
  error={error}
  renderLoadingState={() => <CustomLoadingSkeleton />}
  renderErrorState={(error) => <CustomErrorDisplay error={error} />}
  renderEmptyState={() => <CustomEmptyState />}
/>
```

## Component Architecture

### Core Components
- **DataTable**: Main table component with responsive logic
- **DataTableToolbar**: Search and column visibility controls
- **DataTablePagination**: Pagination controls with page size options
- **MobileCardView**: Mobile-optimized card layout
- **DataTableSkeleton**: Loading state skeleton
- **DataTableError**: Error state display
- **DataTableEmpty**: Empty state display

### File Structure
```
app/components/data-display/data-table/
├── DataTable.tsx              # Main component
├── DataTableToolbar.tsx       # Search and controls
├── DataTablePagination.tsx    # Pagination component
├── MobileCardView.tsx         # Mobile card layout
├── DataTableSkeleton.tsx      # Loading skeleton
├── DataTableError.tsx         # Error state
├── DataTableEmpty.tsx         # Empty state
├── types.ts                   # TypeScript definitions
└── index.ts                   # Exports
```

## Migration Guide

### From InventoryTable

**Before:**
```tsx
<InventoryTable
  inventoryItems={items}
  onView={handleView}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```

**After:**
```tsx
<DataTable
  data={items}
  columns={inventoryColumns}
  renderRowActions={(item) => (
    <InventoryRowActions
      item={item}
      onView={handleView}
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  )}
  caption="Inventory items with stock levels"
/>
```

## Testing

The DataTable component includes comprehensive testing:

```tsx
import { DataTableTest } from '@/app/components/data-display/data-table/DataTable.test';

// Basic functionality test
<DataTableTest />
```

## Performance Considerations

1. **Large Datasets**: Use server-side pagination for datasets > 1000 rows
2. **Complex Cells**: Memoize expensive cell renderers
3. **Mobile Performance**: Limit mobile columns to essential data only
4. **Search Performance**: Implement debounced search for large datasets

## Accessibility Compliance

The DataTable component meets WCAG 2.1 AA standards:

- ✅ Proper table structure with headers and scope attributes
- ✅ Keyboard navigation support
- ✅ Screen reader compatibility
- ✅ High contrast support
- ✅ Focus management
- ✅ ARIA labels and descriptions

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Next Steps

1. **Phase 2 Implementation**: Replace existing table components
2. **Performance Testing**: Benchmark with large datasets
3. **User Testing**: Validate mobile experience
4. **Documentation**: Create component storybook entries
