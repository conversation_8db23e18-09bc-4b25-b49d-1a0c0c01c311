# Quick Reference: Standardized Components

**Project**: Trend_IMS  
**Status**: 92.3% Standardization Complete  
**Last Updated**: 2025-07-03

## 🚀 **Quick Start**

### Import Patterns
```tsx
// ✅ Centralized imports
import { UnifiedCard } from '@/app/components/layout';
import { DataTable } from '@/app/components/data-display/data-table';
import { LoadingOverlay, LoadingCard } from '@/app/components/data-display/loading';
import { ErrorAlert, FormErrorDisplay } from '@/app/components/feedback';
import { useEnhancedTheme, useErrorHandler } from '@/app/hooks';
```

## 📋 **Component Quick Reference**

### 🎨 **Theme Integration** (96% Complete)
```tsx
// ✅ Use semantic variables
className="bg-primary text-primary-foreground"
className="bg-success/10 text-success border-success/20"

// ✅ Theme hooks
const { theme, themeClasses } = useEnhancedTheme();
const { primary, success, warning } = useStatusColors();
```

### 🔄 **Loading States** (90% Complete)
```tsx
// ✅ Standard loading components
<LoadingOverlay message="Saving..." />
<LoadingCard message="Loading data..." />
<LoadingInline size="sm" />
<PageLoadingSkeleton contentType="grid" />
<ChartLoadingSkeleton title="Analytics" />
```

### ⚠️ **Error Handling** (75% Complete - Infrastructure Ready)
```tsx
// ✅ Error components
<ErrorAlert error={error} onRetry={handleRetry} />
<ErrorBanner error={pageError} />
<FormErrorDisplay error={formError} context="assembly-form" />
<InlineError error={fieldError} />

// ✅ Error hook
const { handleError } = useErrorHandler();
```

### 🃏 **Cards** (95% Complete)
```tsx
// ✅ UnifiedCard variants
<UnifiedCard variant="status" title="Status" status="active" color="green">
  Content
</UnifiedCard>

<UnifiedCard variant="interactive" onClick={handleClick} animation="subtle">
  Clickable content
</UnifiedCard>
```

### 📊 **Tables** (100% Complete)
```tsx
// ✅ DataTable (all tables migrated)
<DataTable
  data={data}
  columns={columns}
  searchable
  filterable
  pagination
  mobileResponsive
/>
```

### 📝 **Forms** (90% Complete)
```tsx
// ✅ Form validation pattern
const form = useForm<FormData>({
  resolver: zodResolver(schema),
  mode: 'onChange'
});

// ✅ Error display
<FormErrorDisplay 
  error={form.formState.errors.root}
  onRetry={() => form.clearErrors()}
/>
```

## 🔧 **Development Patterns**

### **Component Creation Template**
```tsx
import { cn } from '@/app/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const variants = cva("base-classes", {
  variants: {
    variant: { default: "default-classes" },
    size: { md: "medium-classes" },
  },
  defaultVariants: { variant: "default", size: "md" },
});

interface Props extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof variants> {}

export function Component({ className, variant, size, ...props }: Props) {
  return (
    <div className={cn(variants({ variant, size }), className)} {...props} />
  );
}
```

### **Error Handling Pattern**
```tsx
const handleAsyncOperation = async () => {
  try {
    setLoading(true);
    await operation();
  } catch (error) {
    handleError(error, { context: 'operation-name' });
  } finally {
    setLoading(false);
  }
};
```

### **Loading State Pattern**
```tsx
// ✅ Conditional loading
{isLoading ? (
  <LoadingCard message="Loading..." />
) : (
  <DataContent />
)}

// ✅ Overlay loading
{isSubmitting && <LoadingOverlay message="Saving..." />}
```

## ✅ **Migration Checklist**

### **Before Creating Components**
- [ ] Check if standardized component exists
- [ ] Use CVA for variants
- [ ] Include accessibility features
- [ ] Support theme integration

### **When Using Components**
- [ ] Import from centralized exports
- [ ] Use semantic CSS variables
- [ ] Include proper ARIA attributes
- [ ] Test with all themes

### **Code Review Items**
- [ ] Uses standardized components
- [ ] Follows theme patterns
- [ ] Has error handling
- [ ] Includes loading states
- [ ] Meets accessibility standards

## 📊 **Current Status**

### **Standardization Scores**
| Component | Score | Status |
|-----------|-------|---------|
| Tables | 100% | ✅ Complete |
| Buttons | 100% | ✅ Complete |
| Cards | 95% | ✅ Nearly Complete |
| Theme | 96% | ✅ Exceeds Target |
| Forms | 90% | ✅ Standardized |
| Navigation | 90% | ✅ Standardized |
| Loading | 90% | ✅ Standardized |
| Errors | 75% | ⚠️ Infrastructure Ready |

### **Overall: 92.3%** (Target: 95%)

## 🎯 **Next Steps for 95%**

1. **Error Handling Adoption**
   - Replace custom error implementations
   - Use `FormErrorDisplay` in forms
   - Implement `ErrorToast` for notifications

2. **Final Migrations**
   - Complete remaining card migrations
   - Update legacy loading implementations
   - Remove custom error handling

## 📚 **Documentation Links**

- [Developer Guidelines](developer-guidelines-standardized-patterns.md)
- [Component Inventory](component-inventory.md)
- [Design System](design-system-documentation.md)
- [Validation Report](final-component-standardization-validation.md)

## 🆘 **Common Issues**

### **Theme Not Working**
```tsx
// ❌ Don't use hardcoded colors
className="bg-blue-500"

// ✅ Use semantic variables
className="bg-primary"
```

### **Loading States**
```tsx
// ❌ Don't create custom loading
<div className="animate-spin">Loading...</div>

// ✅ Use standardized loading
<LoadingSpinner />
```

### **Error Handling**
```tsx
// ❌ Don't use custom error display
<div className="text-red-500">{error.message}</div>

// ✅ Use standardized error components
<ErrorAlert error={error} />
```

---

**Remember**: All standardized systems are implemented and ready for use. Focus on adoption over creation!
