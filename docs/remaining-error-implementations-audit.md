# Remaining Custom Error Implementations Audit

**Project**: Trend_IMS Final Adoption Phase  
**Target**: Achieve 95%+ Component Standardization  
**Current**: 92.3% (Error Handling: 75%)  
**Date**: 2025-07-03

## Overview

This audit identifies remaining custom error implementations that need migration to standardized error handling components to achieve the 95%+ standardization target.

## 🎯 **Migration Impact Analysis**

### **Estimated Standardization Gain**: +3.2%
- **Current Error Handling Score**: 75%
- **Target After Migration**: 95%+ (Infrastructure complete)
- **Overall Standardization**: 92.3% → 95.5%

## 🔴 **High Priority Migrations** (Immediate Impact)

### **1. Direct toast.error Usage** (12 instances found)

#### **app/components/forms/BatchForm/BatchFormClient.tsx**
```tsx
// ❌ Line 243: Direct toast.error usage
toast.error("Failed to save batch");

// ✅ Should use:
import { showErrorToast } from '@/app/components/feedback';
showErrorToast(error, { context: 'batch-form-submission' });
```
**Impact**: High-usage form component

#### **app/(main)/inventory-transactions/page.tsx**
```tsx
// ❌ Line 282: Direct toast.error usage
toast.error('Could not load inventory transactions');

// ✅ Should use:
import { showNetworkErrorToast } from '@/app/components/feedback';
showNetworkErrorToast(fetchTransactions, { customMessage: 'Could not load inventory transactions' });
```
**Impact**: Main page component with high traffic

#### **app/contexts/AssemblyFormContext.tsx**
```tsx
// ❌ Line 256: Direct toast.error usage
toast.error(err instanceof Error ? err.message : 'Failed to load assembly');

// ❌ Line 746: Direct toast.error usage  
toast.error('Failed to refresh stock data');

// ✅ Should use:
import { useErrorHandler } from '@/app/hooks/useErrorHandler';
const { handleError } = useErrorHandler();
handleError(err, { context: 'assembly-loading' });
```
**Impact**: Critical context component affecting all assembly operations

#### **app/components/forms/HierarchicalPartsForm.tsx**
```tsx
// ❌ Line 1093: Direct toast.error usage
toast.error(form.formState.errors.partsRequired.message);

// ✅ Should use:
import { showValidationErrorToast } from '@/app/components/feedback';
showValidationErrorToast(form.formState.errors.partsRequired.message);
```
**Impact**: Complex form component

#### **app/(main)/inventory/page.tsx**
```tsx
// ❌ Line 680: Direct toast.error usage
toast.error(`Failed to delete part: ${errorMessage}`);

// ✅ Should use:
import { showErrorToast } from '@/app/components/feedback';
showErrorToast(error, { context: 'part-deletion' });
```
**Impact**: Main inventory page

#### **app/components/forms/UnifiedAssemblyForm.tsx**
```tsx
// ❌ Line 324: Direct toast.error usage
toast.error(errorMessage);

// ❌ Line 564: Direct toast.error usage
toast.error(errorMessage);

// ✅ Should use:
import { showErrorToast } from '@/app/components/feedback';
showErrorToast(error, { context: 'assembly-form-save' });
```
**Impact**: Critical unified assembly form

#### **app/(main)/assemblies/[id]/page.tsx**
```tsx
// ❌ Line 92: Direct toast.error usage
toast.error('Failed to load assembly');

// ✅ Should use:
import { showNetworkErrorToast } from '@/app/components/feedback';
showNetworkErrorToast(fetchAssembly, { customMessage: 'Failed to load assembly' });
```
**Impact**: Assembly detail page

#### **app/contexts/AssembliesContext.tsx**
```tsx
// ❌ Line 376: Direct toast.error usage
toast.error('Assembly details or assembly code is missing, cannot duplicate.');

// ✅ Should use:
import { showValidationErrorToast } from '@/app/components/feedback';
showValidationErrorToast('Assembly details or assembly code is missing, cannot duplicate.');
```
**Impact**: Core assemblies context

#### **app/(main)/batch-tracking/page.tsx**
```tsx
// ❌ Line 205: Direct toast.error usage
toast.error('Failed to load batch data');

// ✅ Should use:
import { showNetworkErrorToast } from '@/app/components/feedback';
showNetworkErrorToast(fetchBatches, { customMessage: 'Failed to load batch data' });
```
**Impact**: Batch tracking page

### **2. Custom Error Display Components** (5 instances found)

#### **app/components/features/SentryIssuesViewer.tsx**
```tsx
// ❌ Lines 160-166: Custom Alert error display
{error && (
  <Alert variant="destructive">
    <AlertCircle className="h-4 w-4" />
    <AlertTitle>Error</AlertTitle>
    <AlertDescription>{error}</AlertDescription>
  </Alert>
)}

// ✅ Should use:
import { ErrorAlert } from '@/app/components/feedback';
{error && <ErrorAlert error={error} />}
```
**Impact**: Feature component for error monitoring

#### **app/components/forms/FormContainer/FormContainerClient.tsx**
```tsx
// ❌ Lines 53-61: Custom Alert error display
{error && (
  <div className="px-6">
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{error}</AlertDescription>
    </Alert>
  </div>
)}

// ✅ Should use:
import { FormErrorDisplay } from '@/app/components/feedback';
{error && <FormErrorDisplay error={error} />}
```
**Impact**: Core form container component used across multiple forms

## 🟡 **Medium Priority Migrations** (Secondary Impact)

### **3. Manual Error Handling Without useErrorHandler** (8 instances found)

#### **Multiple Components with try/catch blocks**
- `app/contexts/AssemblyFormContext.tsx` (Lines 254-261)
- `app/(main)/inventory/page.tsx` (Lines 664-684)
- `app/components/forms/UnifiedAssemblyForm.tsx` (Lines 559-566)
- `app/(main)/assemblies/[id]/page.tsx` (Lines 89-96)
- `app/(main)/batch-tracking/page.tsx` (Lines 202-207)

**Pattern**: Manual try/catch without standardized error handling
```tsx
// ❌ Current pattern
try {
  await operation();
} catch (error) {
  console.error('Error:', error);
  toast.error('Operation failed');
}

// ✅ Should use:
import { useErrorHandler } from '@/app/hooks/useErrorHandler';
const { handleError } = useErrorHandler();

try {
  await operation();
} catch (error) {
  handleError(error, { context: 'operation-name' });
}
```

## 📊 **Migration Priority Matrix**

| Component | Type | Impact | Effort | Priority |
|-----------|------|--------|--------|----------|
| AssemblyFormContext | Context | High | Medium | 🔴 Critical |
| FormContainerClient | Form Core | High | Low | 🔴 Critical |
| UnifiedAssemblyForm | Form | High | Medium | 🔴 Critical |
| inventory-transactions/page | Page | High | Low | 🔴 Critical |
| inventory/page | Page | High | Low | 🔴 Critical |
| BatchFormClient | Form | Medium | Low | 🟡 High |
| HierarchicalPartsForm | Form | Medium | Low | 🟡 High |
| assemblies/[id]/page | Page | Medium | Low | 🟡 High |
| AssembliesContext | Context | Medium | Medium | 🟡 High |
| SentryIssuesViewer | Feature | Low | Low | 🟢 Medium |
| batch-tracking/page | Page | Low | Low | 🟢 Medium |

## 🎯 **Migration Strategy**

### **Phase 1: Critical Components** (Day 1)
1. **AssemblyFormContext.tsx** - Replace 2 toast.error instances with useErrorHandler
2. **FormContainerClient.tsx** - Replace custom Alert with FormErrorDisplay
3. **UnifiedAssemblyForm.tsx** - Replace 2 toast.error instances with showErrorToast
4. **inventory-transactions/page.tsx** - Replace toast.error with showNetworkErrorToast
5. **inventory/page.tsx** - Replace toast.error with showErrorToast

### **Phase 2: High Priority Components** (Day 1-2)
1. **BatchFormClient.tsx** - Replace toast.error with showErrorToast
2. **HierarchicalPartsForm.tsx** - Replace toast.error with showValidationErrorToast
3. **assemblies/[id]/page.tsx** - Replace toast.error with showNetworkErrorToast
4. **AssembliesContext.tsx** - Replace toast.error with showValidationErrorToast

### **Phase 3: Remaining Components** (Day 2)
1. **SentryIssuesViewer.tsx** - Replace custom Alert with ErrorAlert
2. **batch-tracking/page.tsx** - Replace toast.error with showNetworkErrorToast

## 📈 **Expected Results**

### **After Phase 1 Migration**:
- **Error Handling Score**: 75% → 85%
- **Overall Standardization**: 92.3% → 93.8%

### **After Phase 2 Migration**:
- **Error Handling Score**: 85% → 92%
- **Overall Standardization**: 93.8% → 95.1%

### **After Phase 3 Migration**:
- **Error Handling Score**: 92% → 95%
- **Overall Standardization**: 95.1% → 95.5%

## ✅ **Success Criteria**

### **Component-Level Success**
- [ ] Zero direct `toast.error()` usage
- [ ] Zero custom Alert error displays
- [ ] All error handling uses standardized components or hooks
- [ ] Consistent error messaging and user experience

### **System-Level Success**
- [ ] **95%+ Overall Standardization** achieved
- [ ] **95%+ Error Handling Standardization** achieved
- [ ] All error handling follows established patterns
- [ ] Comprehensive error logging and correlation

## 🔧 **Implementation Notes**

### **Import Patterns**
```tsx
// Standard error handling imports
import { 
  showErrorToast, 
  showValidationErrorToast, 
  showNetworkErrorToast 
} from '@/app/components/feedback';
import { useErrorHandler } from '@/app/hooks/useErrorHandler';
import { ErrorAlert, FormErrorDisplay } from '@/app/components/feedback';
```

### **Context Considerations**
- **Forms**: Use `showValidationErrorToast` for validation errors
- **Network Operations**: Use `showNetworkErrorToast` with retry functionality
- **General Errors**: Use `showErrorToast` with appropriate context
- **Component Errors**: Use `ErrorAlert` or `FormErrorDisplay` for display
- **Complex Operations**: Use `useErrorHandler` hook for comprehensive handling

---

**Next Steps**: Begin Phase 1 migration with critical components to achieve immediate standardization gains.
