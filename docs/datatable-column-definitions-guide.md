# DataTable Column Definitions Guide

## Overview

This guide explains how to use the standardized DataTable column definitions for consistent table implementations across the Trend_IMS application.

## Available Column Definitions

### 1. Products Table Columns

#### Simple Mode
```tsx
import { DataTable, createProductsSimpleColumns, ProductColumnData } from '@/app/components/data-display/data-table';

const ProductsSimpleTable = ({ products }: { products: ProductColumnData[] }) => {
  const actions = {
    onView: (product) => console.log('View:', product),
    onEdit: (product) => console.log('Edit:', product),
    onDelete: (product) => console.log('Delete:', product),
    onRefresh: () => console.log('Refresh'),
  };

  const columns = createProductsSimpleColumns(actions);

  return (
    <DataTable
      data={products}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
    />
  );
};
```

#### Complex Mode
```tsx
const ProductsComplexTable = ({ products }: { products: ProductColumnData[] }) => {
  const actions = {
    onView: (product) => router.push(`/products/${product._id}`),
    onEdit: (product) => router.push(`/products/${product._id}/edit`),
    onDelete: (product) => handleDelete(product),
    onRefresh: () => refetch(),
  };

  const columns = createProductsComplexColumns(actions);

  return (
    <DataTable
      data={products}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
    />
  );
};
```

### 2. Assemblies Table Columns

```tsx
import { DataTable, createAssembliesColumns, AssemblyColumnData } from '@/app/components/data-display/data-table';

const AssembliesTable = ({ assemblies }: { assemblies: AssemblyColumnData[] }) => {
  const actions = {
    onView: (assembly) => router.push(`/assemblies/${assembly._id}`),
    onEdit: (assembly) => router.push(`/assemblies/${assembly._id}/edit`),
    onDelete: (assembly) => handleDelete(assembly),
    onDuplicate: (assembly) => handleDuplicate(assembly),
    onRefresh: () => refetch(),
  };

  const columns = createAssembliesColumns(actions, false); // false = complex mode

  return (
    <DataTable
      data={assemblies}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
      // Enable expandable rows for assemblies
      renderRowActions={(assembly) => (
        <div className="p-4 bg-muted/50">
          <h4 className="font-semibold mb-2">Parts Required:</h4>
          {assembly.partsRequired?.map((part, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span>{part.partName}</span>
              <span>{part.quantityRequired} required, {part.currentStock || 0} available</span>
            </div>
          ))}
        </div>
      )}
    />
  );
};
```

### 3. Inventory Table Columns

```tsx
import { DataTable, createInventoryColumns, InventoryColumnData } from '@/app/components/data-display/data-table';

const InventoryTable = ({ inventory }: { inventory: InventoryColumnData[] }) => {
  const actions = {
    onView: (item) => setSelectedItem(item),
    onEdit: (item) => router.push(`/inventory/${item._id}/edit`),
    onDelete: (item) => handleDelete(item),
  };

  const columns = createInventoryColumns(actions);

  return (
    <DataTable
      data={inventory}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
      initialSorting={[{ id: 'status', desc: true }]} // Show low stock first
    />
  );
};
```

### 4. Work Orders Table Columns

#### Simple Mode
```tsx
import { DataTable, createWorkOrdersSimpleColumns, WorkOrderColumnData } from '@/app/components/data-display/data-table';

const WorkOrdersSimpleTable = ({ workOrders }: { workOrders: WorkOrderColumnData[] }) => {
  const actions = {
    onView: (workOrder) => console.log('View:', workOrder),
    onEdit: (workOrder) => console.log('Edit:', workOrder),
    onDelete: (workOrder) => console.log('Delete:', workOrder),
    onRefresh: () => console.log('Refresh'),
  };

  const columns = createWorkOrdersSimpleColumns(actions);

  return (
    <DataTable
      data={workOrders}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
    />
  );
};
```

#### Complex Mode
```tsx
const WorkOrdersComplexTable = ({ workOrders }: { workOrders: WorkOrderColumnData[] }) => {
  const actions = {
    onView: (workOrder) => router.push(`/work-orders/${workOrder._id}`),
    onEdit: (workOrder) => router.push(`/work-orders/${workOrder._id}/edit`),
    onDelete: (workOrder) => handleDelete(workOrder),
    onRefresh: () => refetch(),
  };

  const columns = createWorkOrdersComplexColumns(actions);

  return (
    <DataTable
      data={workOrders}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
      initialSorting={[{ id: 'dueDate', desc: false }]} // Show earliest due dates first
    />
  );
};
```

### 4. Feature Product Table Columns

```tsx
import { DataTable, createFeatureProductColumns, FeatureProductColumnData } from '@/app/components/data-display/data-table';

const FeatureProductTable = ({ products }: { products: FeatureProductColumnData[] }) => {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  const actions = {
    onView: (product) => setSelectedProduct(product),
    onEdit: (product) => handleEdit(product),
    onEditProduct: (product) => handleEditProduct(product),
    onDelete: (id) => handleDelete(id),
    onDeleteProduct: (id) => handleDeleteProduct(id),
  };

  const handleToggleSelect = (id: string) => {
    setSelectedProducts(prev => 
      prev.includes(id) 
        ? prev.filter(p => p !== id)
        : [...prev, id]
    );
  };

  const handleToggleSelectAll = () => {
    setSelectedProducts(prev => 
      prev.length === products.length ? [] : products.map(p => p.id)
    );
  };

  const columns = createFeatureProductColumns(
    actions,
    selectedProducts,
    handleToggleSelect,
    handleToggleSelectAll
  );

  return (
    <DataTable
      data={products}
      columns={columns}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={true}
      enablePagination={true}
      mobileDisplayMode="cards"
      renderToolbar={() => (
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            disabled={selectedProducts.length === 0}
            onClick={() => handleBulkAction('edit')}
          >
            Edit Selected ({selectedProducts.length})
          </Button>
          <Button 
            variant="destructive" 
            disabled={selectedProducts.length === 0}
            onClick={() => handleBulkAction('delete')}
          >
            Delete Selected ({selectedProducts.length})
          </Button>
        </div>
      )}
    />
  );
};
```

## Key Features

### Mobile Responsiveness
- **mobilePriority**: Controls column order on mobile (1 = highest priority)
- **hideOnMobile**: Hides columns on mobile devices
- **mobileRender**: Custom mobile card rendering

### Search and Filtering
- **searchable**: Makes columns searchable via global search
- **filterFn**: Custom filter functions for complex filtering

### Actions
- All column definitions include standardized action patterns
- Actions are passed as props for flexibility
- Consistent dropdown menus and button layouts

### Accessibility
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility

## Migration Benefits

1. **Consistency**: All tables use the same patterns and styling
2. **Mobile-First**: Responsive design with card fallbacks
3. **Accessibility**: WCAG 2.1 AA compliance
4. **Performance**: Optimized rendering with TanStack Table
5. **Maintainability**: Centralized column definitions
6. **Flexibility**: Customizable actions and behaviors

## Next Steps

1. Replace existing table implementations with these column definitions
2. Test mobile responsiveness and accessibility
3. Update imports throughout the codebase
4. Remove legacy table components
