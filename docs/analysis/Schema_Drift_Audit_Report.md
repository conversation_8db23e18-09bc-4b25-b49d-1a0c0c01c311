# Schema Drift Audit Report

## Executive Summary

This report documents the comprehensive analysis of schema inconsistencies between the canonical database schema (database_schema_updated.md) and the current Mongoose models and frontend implementations. Critical misalignments have been identified that are causing data integrity issues and runtime errors.

## Critical Schema Drift Issues

### 1. Assembly Model Schema Drift

**Current Model (assembly.model.ts):**
- ✅ Uses `assemblyCode` (matches canonical)
- ✅ Uses `partsRequired` array (matches canonical)
- ❌ Uses `quantity` in partsRequired (should be `quantityRequired`)
- ✅ Status enum includes canonical values + extras: `['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived']`
- ❌ Missing `productId` field from canonical schema
- ❌ Uses `parentId` instead of canonical schema structure
- ❌ Missing `isTopLevel` field (implemented as virtual)
- ❌ Missing `manufacturingInstructions` field
- ❌ Missing `estimatedBuildTime` field

**Frontend Usage Issues:**
- Frontend components expect status values: `['active', 'draft', 'archived']`
- AssemblyFormContext uses `manufacturingInstructions` and `estimatedBuildTime` fields not in current model
- Components access both `quantity` and `quantityRequired` causing confusion

### 2. Part Model Schema Drift

**Current Model (part.model.ts):**
- ✅ Uses nested `inventory` object (matches canonical)
- ✅ Inventory structure matches canonical schema exactly
- ✅ Uses `partNumber` as unique identifier (matches canonical)
- ✅ Status enum matches canonical: `['active', 'inactive', 'obsolete']`
- ✅ All required fields present and correctly typed

**Frontend Usage Issues:**
- Some components try to access `currentStock` at top level instead of `inventory.currentStock`
- Legacy code still references `reorderLevel` at top level instead of `inventory.reorderLevel`

### 3. Work Order Model Alignment

**Current Model (workOrder.model.ts):**
- ✅ Fully aligned with canonical schema
- ✅ All field names match exactly
- ✅ Status enum matches canonical values
- ✅ Proper ObjectId references

### 4. Status Enum Inconsistencies

**Assembly Status Values:**
- Canonical: `['active', 'pending_review', 'in_production', 'obsolete']`
- Current Model: `['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived']`
- Frontend Expects: `['active', 'draft', 'archived']`

**Part Status Values:**
- Canonical: `['active', 'inactive', 'obsolete']`
- Current Model: `['active', 'inactive', 'obsolete']` ✅
- Frontend Usage: Consistent ✅

### 5. Field Name Mapping Issues

**Assembly partsRequired Array:**
- Canonical Schema: `quantityRequired`
- Current Model: `quantity`
- Frontend: Uses both `quantity` and `quantityRequired` inconsistently

**Inventory Access Patterns:**
- Canonical: `part.inventory.currentStock`
- Some Frontend Code: `part.currentStock` (incorrect)
- Some Frontend Code: `part.inventory.currentStock` (correct)

## Legacy Code Issues

### 1. Deprecated Files
- `app/(main)/product/[id]/page.tsx` - Uses old Supabase schema
- Multiple duplicate form components (PartForm vs EnhancedPartForm)
- Old assembly schema references in documentation

### 2. Inconsistent Data Access
- Multiple ways to access the same data fields
- Inconsistent field name transformations
- Mixed use of legacy and canonical field names

## Impact Assessment

### High Impact Issues
1. **Assembly partsRequired field mismatch** - Causes form submission failures
2. **Status enum mismatches** - Breaks filtering and status updates
3. **Inventory access inconsistencies** - Causes stock display errors

### Medium Impact Issues
1. **Missing canonical fields** - Limits functionality expansion
2. **Legacy code presence** - Increases maintenance burden
3. **Inconsistent error handling** - Makes debugging difficult

### Low Impact Issues
1. **Documentation inconsistencies** - Confuses developers
2. **Unused imports and commented code** - Code quality issues

## Recommendations

### Immediate Actions Required
1. **Fix Assembly Model Schema** - Align with canonical schema
2. **Standardize Status Enums** - Create centralized enum definitions
3. **Fix Frontend Data Access** - Ensure consistent inventory field access
4. **Remove Legacy Code** - Clean up deprecated files and components

### Medium-term Actions
1. **Create Shared Type Definitions** - Generate from Mongoose schemas
2. **Implement Schema Validation** - Add runtime validation
3. **Add Database Indexes** - Optimize query performance
4. **Standardize Error Handling** - Consistent API responses

### Long-term Actions
1. **Schema Migration Strategy** - Plan for future schema changes
2. **Automated Testing** - Prevent schema drift
3. **Documentation Updates** - Keep schemas synchronized

## Next Steps

1. Begin with Assembly model alignment (highest impact)
2. Standardize status enums across system
3. Fix frontend inventory access patterns
4. Remove legacy and deprecated code
5. Create comprehensive tests to prevent regression

This audit provides the foundation for systematic schema alignment and improved data consistency across the application.
