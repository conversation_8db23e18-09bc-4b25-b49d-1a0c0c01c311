# Developer Guidelines: Standardized Component Patterns

**Project**: Trend_IMS Component Standardization  
**Version**: 2.0  
**Last Updated**: 2025-07-03  
**Status**: ✅ **ACTIVE** - All standardized systems implemented

## Overview

This guide provides comprehensive guidelines for using and maintaining the standardized component patterns in Trend_IMS. All developers should follow these patterns to ensure consistency, maintainability, and optimal user experience.

## 🎯 **Standardization Achievement**

- **Overall Score**: 92.3% (approaching 95% target)
- **Infrastructure**: 100% complete
- **Theme Integration**: 96% (exceeds target)
- **Component Systems**: All implemented and ready for use

## Core Principles

### 1. **Consistency First**
- Always use standardized components over custom implementations
- Follow established patterns for props, styling, and behavior
- Maintain consistent naming conventions across components

### 2. **Accessibility by Default**
- All components include WCAG 2.1 AA compliance
- Proper ARIA attributes and keyboard navigation
- Screen reader compatibility built-in

### 3. **Theme Integration**
- Use semantic CSS variables instead of hardcoded colors
- Leverage theme hooks for dynamic styling
- Support all 8 theme variants automatically

### 4. **Performance Optimization**
- Prefer standardized components for bundle size efficiency
- Use proper memoization and lazy loading patterns
- Follow server/client component separation

## Component Usage Guidelines

### 🎨 **Theme Integration**

#### ✅ **DO: Use Semantic CSS Variables**
```tsx
// ✅ Correct - semantic variables
className="bg-primary text-primary-foreground border-primary/20"

// ✅ Correct - status colors
className="bg-success/10 text-success border-success/20"
```

#### ❌ **DON'T: Use Hardcoded Colors**
```tsx
// ❌ Incorrect - hardcoded colors
className="bg-blue-500 text-white border-blue-200"

// ❌ Incorrect - manual theme checks
{theme.mode === 'dark' ? 'bg-gray-800' : 'bg-gray-100'}
```

#### **Theme Hooks Usage**
```tsx
import { useEnhancedTheme, useThemeColors, useStatusColors } from '@/app/hooks/theme';

function MyComponent() {
  const { theme, themeClasses } = useEnhancedTheme();
  const { primary, success, warning } = useStatusColors();
  
  return (
    <div className={themeClasses.card}>
      <span className={`text-${success}`}>Success message</span>
    </div>
  );
}
```

### 🔄 **Loading States**

#### **Standard Loading Components**
```tsx
import { 
  LoadingOverlay, 
  LoadingInline, 
  LoadingCard,
  PageLoadingSkeleton,
  ChartLoadingSkeleton 
} from '@/app/components/data-display/loading';

// ✅ Form overlay loading
<LoadingOverlay message="Saving assembly..." />

// ✅ Button loading
<LoadingInline size="sm" />

// ✅ Page loading
<PageLoadingSkeleton contentType="grid" showBreadcrumb />
```

#### **Migration Pattern**
```tsx
// ❌ Before - custom loading
{isLoading && (
  <div className="flex items-center justify-center p-4">
    <Loader2 className="h-6 w-6 animate-spin" />
    <span>Loading...</span>
  </div>
)}

// ✅ After - standardized loading
{isLoading && <LoadingCard message="Loading data..." />}
```

### ⚠️ **Error Handling**

#### **Standard Error Components**
```tsx
import { 
  ErrorAlert, 
  ErrorBanner, 
  FormErrorDisplay,
  InlineError,
  ErrorToast,
  useErrorHandler 
} from '@/app/components/feedback';

// ✅ Form errors
<FormErrorDisplay 
  error={formError} 
  onRetry={handleRetry}
  context="assembly-form" 
/>

// ✅ Inline field errors
<InlineError error={fieldError} />

// ✅ Page-level errors
<ErrorBanner 
  error={pageError}
  action={{ label: "Retry", action: handleRetry }}
/>
```

#### **Error Handling Hook**
```tsx
import { useErrorHandler } from '@/app/hooks/useErrorHandler';

function MyComponent() {
  const { handleError } = useErrorHandler();
  
  const handleSubmit = async () => {
    try {
      await submitData();
    } catch (error) {
      handleError(error, { context: 'form-submission' });
    }
  };
}
```

### 🃏 **Card Components**

#### **UnifiedCard Usage**
```tsx
import { UnifiedCard } from '@/app/components/layout';

// ✅ Status card
<UnifiedCard 
  variant="status" 
  title="Assembly Status"
  status="active"
  color="green"
>
  Content
</UnifiedCard>

// ✅ Interactive card
<UnifiedCard 
  variant="interactive"
  onClick={handleClick}
  animation="subtle"
>
  Clickable content
</UnifiedCard>
```

### 📊 **Table Components**

#### **DataTable Usage**
```tsx
import { DataTable } from '@/app/components/data-display/data-table';

// ✅ Standard table implementation
<DataTable
  data={inventoryData}
  columns={inventoryColumns}
  searchable
  filterable
  pagination
  mobileResponsive
/>
```

### 📝 **Form Components**

#### **Form Validation Pattern**
```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormErrorDisplay } from '@/app/components/feedback';

const form = useForm<FormData>({
  resolver: zodResolver(formSchema),
  defaultValues,
  mode: 'onChange'
});

// ✅ Standardized form error handling
{form.formState.errors.root && (
  <FormErrorDisplay 
    error={form.formState.errors.root}
    onRetry={() => form.clearErrors()}
  />
)}
```

## Development Workflows

### 🚀 **Creating New Components**

#### **1. Component Structure**
```tsx
// ✅ Standard component template
import { cn } from '@/app/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const componentVariants = cva(
  "base-classes",
  {
    variants: {
      variant: {
        default: "default-classes",
        secondary: "secondary-classes",
      },
      size: {
        sm: "small-classes",
        md: "medium-classes",
        lg: "large-classes",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

interface ComponentProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof componentVariants> {
  // Additional props
}

export function Component({ 
  className, 
  variant, 
  size, 
  ...props 
}: ComponentProps) {
  return (
    <div 
      className={cn(componentVariants({ variant, size }), className)}
      {...props}
    />
  );
}
```

#### **2. Export Pattern**
```tsx
// ✅ Centralized exports
// app/components/category/index.ts
export { Component } from './Component';
export type { ComponentProps } from './Component';

// ✅ Main export
// app/components/index.ts
export * from './category';
```

### 🧪 **Testing Standards**

#### **Component Testing Template**
```tsx
import { render, screen } from '@testing-library/react';
import { Component } from './Component';

describe('Component', () => {
  it('renders with default props', () => {
    render(<Component>Test content</Component>);
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies variant classes correctly', () => {
    render(<Component variant="secondary">Content</Component>);
    // Test variant-specific classes
  });

  it('supports accessibility features', () => {
    render(<Component aria-label="Test component">Content</Component>);
    expect(screen.getByLabelText('Test component')).toBeInTheDocument();
  });
});
```

### 📚 **Documentation Standards**

#### **Component Documentation Template**
```markdown
# Component Name

## Overview
Brief description of the component's purpose and functionality.

## Usage
```tsx
import { Component } from '@/app/components/category';

<Component variant="default" size="md">
  Content
</Component>
```

## Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| variant | string | "default" | Component variant |
| size | string | "md" | Component size |

## Examples
[Include common usage examples]

## Accessibility
[Document accessibility features]
```

## Migration Checklist

### ✅ **Before Creating New Components**
- [ ] Check if standardized component exists
- [ ] Review existing patterns for similar functionality
- [ ] Consider if existing component can be extended
- [ ] Follow CVA pattern for variants

### ✅ **When Using Existing Components**
- [ ] Import from centralized exports
- [ ] Use semantic CSS variables for colors
- [ ] Include proper accessibility attributes
- [ ] Test with all theme variants
- [ ] Verify mobile responsiveness

### ✅ **Code Review Checklist**
- [ ] Uses standardized components where applicable
- [ ] Follows theme integration patterns
- [ ] Includes proper error handling
- [ ] Has appropriate loading states
- [ ] Meets accessibility standards
- [ ] Includes TypeScript types
- [ ] Has proper documentation

## Performance Best Practices

### 🚀 **Bundle Optimization**
- Use tree-shaking friendly imports
- Lazy load heavy components
- Prefer standardized components for shared code

### 🎨 **Styling Performance**
- Use CSS variables over inline styles
- Leverage theme class caching
- Minimize style recalculations

### 📱 **Mobile Performance**
- Use responsive design patterns
- Implement proper loading states
- Optimize touch interactions

## Component Migration Checklist

### 🔄 **Migrating Custom Components to Standardized**

#### **Error Handling Migration**
- [ ] Replace custom error states with `ErrorAlert` or `ErrorBanner`
- [ ] Update form error handling to use `FormErrorDisplay`
- [ ] Replace custom toast notifications with `ErrorToast`
- [ ] Implement `useErrorHandler` hook for consistent error management
- [ ] Add error recovery mechanisms where applicable

#### **Loading State Migration**
- [ ] Replace custom loading spinners with `LoadingSpinner` variants
- [ ] Update overlay loading to use `LoadingOverlay`
- [ ] Migrate skeleton loading to `LoadingSkeleton` components
- [ ] Use specialized loading components (`PageLoadingSkeleton`, `ChartLoadingSkeleton`)
- [ ] Remove custom loading implementations

#### **Theme Integration Migration**
- [ ] Replace hardcoded colors with semantic CSS variables
- [ ] Remove manual `theme.mode` checks
- [ ] Implement theme hooks (`useEnhancedTheme`, `useThemeColors`)
- [ ] Update component variants to use theme-aware classes
- [ ] Test with all 8 theme variants

### 📋 **New Component Development Checklist**

#### **Planning Phase**
- [ ] Check if standardized component already exists
- [ ] Review similar components for patterns
- [ ] Plan variant system using CVA
- [ ] Design accessibility features
- [ ] Consider mobile responsiveness

#### **Implementation Phase**
- [ ] Follow standardized component template
- [ ] Implement CVA variant system
- [ ] Add proper TypeScript types
- [ ] Include accessibility attributes
- [ ] Support theme integration
- [ ] Add loading and error states

#### **Testing Phase**
- [ ] Write unit tests for all variants
- [ ] Test accessibility with screen readers
- [ ] Verify mobile responsiveness
- [ ] Test with all theme variants
- [ ] Performance testing

#### **Documentation Phase**
- [ ] Create component documentation
- [ ] Add usage examples
- [ ] Document accessibility features
- [ ] Update centralized exports
- [ ] Add to component inventory

## Support and Resources

### 📖 **Documentation**
- [Component Inventory](component-inventory.md)
- [Design System Documentation](design-system-documentation.md)
- [Theme Integration Audit](theme-integration-audit.md)
- [Loading Components Documentation](loading-components-documentation.md)
- [Final Component Standardization Validation](final-component-standardization-validation.md)
- [Error Handling Audit](error-handling-audit.md)

### 🔧 **Tools**
- CVA for variant management
- Tailwind CSS for styling
- Framer Motion for animations
- Radix UI for accessibility
- React Hook Form + Zod for forms
- TanStack Table for data tables

### 🆘 **Getting Help**
1. Check existing documentation
2. Review similar component implementations
3. Consult the component standardization analysis
4. Follow established patterns in the codebase
5. Reference the developer guidelines for standardized patterns

### 📊 **Current Standardization Status**
- **Overall Score**: 92.3% (approaching 95% target)
- **Infrastructure**: 100% complete
- **Theme Integration**: 96% (exceeds target)
- **Next Focus**: Error handling adoption for final 95% achievement

---

**Remember**: Consistency is key to maintainable code. When in doubt, follow existing patterns and prioritize user experience and accessibility. All standardized systems are implemented and ready for use.
