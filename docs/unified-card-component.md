# Unified Card Component Documentation

## Overview

The Unified Card Component consolidates the functionality of BaseCard, ActionCard, StatusCard, and the basic Card components into a single, flexible component using Class Variance Authority (CVA) for variant management. This approach reduces code duplication, improves maintainability, and provides a consistent API across all card types.

## Features

- **Unified API**: Single component handles all card variants
- **CVA Variants**: Type-safe variant management with excellent IntelliSense
- **Backward Compatibility**: Compatibility wrappers maintain existing APIs
- **Animation Support**: Framer Motion integration with customizable animations
- **Theme Integration**: Full support for the existing theme system
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA attributes
- **TypeScript**: Comprehensive type definitions with variant props

## Component Structure

```
app/components/layout/cards/
├── UnifiedCard.tsx          # Main unified card component
├── card-compat.tsx          # Compatibility wrappers
├── card.tsx                 # Enhanced base card with CVA
├── README.md                # Component documentation
├── BaseCard/                # Legacy BaseCard (deprecated)
├── ActionCard/              # Legacy ActionCard (deprecated)
└── StatusCard/              # Legacy StatusCard (deprecated)
```

## Usage

### Basic Usage

```tsx
import { UnifiedCard } from '@/app/components/layout';

// Default card
<UnifiedCard>
  <h3>Card Title</h3>
  <p>Card content goes here</p>
</UnifiedCard>

// Interactive card
<UnifiedCard 
  variant="interactive" 
  onClick={() => console.log('Card clicked')}
>
  <h3>Clickable Card</h3>
</UnifiedCard>
```

### Variants

#### Default Variants
- `default`: Basic card with hover effects
- `elevated`: Enhanced shadow for prominence
- `interactive`: Clickable with hover states
- `outline`: Border-only styling
- `ghost`: Transparent background

#### Specialized Variants
- `base`: Enhanced card with title, subtitle, icon support
- `action`: Button-like card for actions
- `status`: Data visualization card with progress bars

### Base Card Variant

```tsx
<UnifiedCard
  variant="base"
  title="Inventory Overview"
  subtitle="Current stock levels"
  icon={<Package size={20} />}
  color="blue"
  onViewDetails={() => handleViewDetails()}
  onClick={() => handleCardClick()}
>
  <div className="space-y-2">
    <div className="flex justify-between">
      <span>Total Items</span>
      <span>1,234</span>
    </div>
  </div>
</UnifiedCard>
```

### Action Card Variant

```tsx
<UnifiedCard
  variant="action"
  label="Add Product"
  icon={<Plus size={16} />}
  color="blue"
  onClick={() => handleAddProduct()}
/>
```

### Status Card Variant

```tsx
<UnifiedCard
  variant="status"
  title="Order Status"
  icon={<ShoppingCart />}
  color="green"
  mainStat={{ value: 89, label: 'Active Orders' }}
  data={{
    'Pending': 25,
    'Processing': 40,
    'Shipped': 20,
    'Delivered': 4
  }}
  onClick={() => handleStatusClick()}
/>
```

## Props Reference

### UnifiedCardProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'base' \| 'action' \| 'status' \| 'elevated' \| 'interactive' \| 'outline' \| 'ghost'` | `'default'` | Card variant |
| `size` | `'sm' \| 'md' \| 'lg' \| 'xl'` | `'md'` | Card size |
| `color` | `'default' \| 'blue' \| 'green' \| 'red' \| 'yellow' \| 'purple' \| 'orange' \| 'gray'` | `'default'` | Color theme |
| `animation` | `'none' \| 'subtle' \| 'bounce' \| 'glow'` | `'subtle'` | Animation type |
| `title` | `string` | - | Card title (base/status variants) |
| `subtitle` | `string` | - | Card subtitle (base variant) |
| `icon` | `ReactNode` | - | Icon element |
| `label` | `string` | - | Action label (action variant) |
| `data` | `Record<string, number>` | - | Status data (status variant) |
| `mainStat` | `{ value: number; label: string }` | - | Main statistic (status variant) |
| `onViewDetails` | `() => void` | - | View details handler |
| `viewDetailsText` | `string` | `'View Details'` | View details text |
| `isFeatured` | `boolean` | `false` | Featured styling |
| `animate` | `boolean` | `true` | Enable animations |

## Backward Compatibility

### Compatibility Wrappers

For seamless migration, compatibility wrappers are provided:

```tsx
import {
  BaseCardCompat,
  ActionCardCompat,
  StatusCardCompat
} from '@/app/components/layout';

// Drop-in replacement for BaseCard
<BaseCardCompat
  title="Legacy Card"
  icon={<Package />}
  color="blue"
>
  Content
</BaseCardCompat>
```

### Migration Guide

#### From BaseCard

```tsx
// Before
import { BaseCard } from '@/app/components/layout';

<BaseCard title="Title" icon={<Icon />} color="blue">
  Content
</BaseCard>

// After (Option 1: Direct migration)
import { UnifiedCard } from '@/app/components/layout';

<UnifiedCard variant="base" title="Title" icon={<Icon />} color="blue">
  Content
</UnifiedCard>

// After (Option 2: Compatibility wrapper)
import { BaseCardCompat } from '@/app/components/layout';

<BaseCardCompat title="Title" icon={<Icon />} color="blue">
  Content
</BaseCardCompat>
```

#### From ActionCard

```tsx
// Before
import { ActionCard } from '@/app/components/layout';

<ActionCard label="Add Item" icon={<Plus />} onClick={handler} />

// After
import { UnifiedCard } from '@/app/components/layout';

<UnifiedCard variant="action" label="Add Item" icon={<Plus />} onClick={handler} />
```

#### From StatusCard

```tsx
// Before
import { StatusCard } from '@/app/components/layout';

<StatusCard 
  title="Status" 
  mainStat={{ value: 100, label: 'Total' }}
  data={{ Active: 80, Inactive: 20 }}
/>

// After
import { UnifiedCard } from '@/app/components/layout';

<UnifiedCard 
  variant="status"
  title="Status" 
  mainStat={{ value: 100, label: 'Total' }}
  data={{ Active: 80, Inactive: 20 }}
/>
```

## Styling and Theming

### Color Themes

The component supports the following color themes:
- `blue` - Information/primary actions
- `green` - Success states
- `red` - Error/destructive actions
- `yellow` - Warning states
- `purple` - Primary brand color
- `orange` - Secondary warning
- `gray` - Neutral/disabled states

### Custom Styling

```tsx
<UnifiedCard 
  className="custom-card-class"
  variant="elevated"
  color="blue"
>
  Content
</UnifiedCard>
```

## Animation Options

### Animation Types
- `none`: No animations
- `subtle`: Gentle hover effects (default)
- `bounce`: More pronounced movement
- `glow`: Glowing shadow effects

```tsx
<UnifiedCard animation="bounce" variant="interactive">
  Bouncy card
</UnifiedCard>
```

## Accessibility

The unified card component includes:
- Proper ARIA attributes for interactive elements
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance (WCAG 2.1 AA)

## Performance

- **Memoized Components**: Optimized re-rendering
- **Lazy Loading**: Framer Motion animations loaded on demand
- **Tree Shaking**: Only used variants are included in bundle
- **TypeScript**: Compile-time optimization

## Testing

The component includes comprehensive test coverage:
- Unit tests for all variants
- Integration tests with theme system
- Accessibility tests
- Visual regression tests

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

When contributing to the unified card component:
1. Maintain backward compatibility
2. Add comprehensive TypeScript types
3. Include accessibility considerations
4. Update documentation and examples
5. Add appropriate tests

## Related Components

- [DataTable Component](./data-table-component.md)
- [Button Component](./button-component.md)
- [Theme System](./theme-system.md)
