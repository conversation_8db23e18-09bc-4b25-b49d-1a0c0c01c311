# Standardized Loading Components Documentation

**Date**: January 3, 2025  
**Project**: Trend_IMS  
**Status**: Phase 1 - Loading System Implemented

## Overview

The standardized loading component system provides a comprehensive set of loading indicators that replace all custom loading implementations across the application. This system ensures consistent user experience, proper theme integration, and accessibility compliance.

## Architecture

### Core Components

1. **LoadingSpinner** - Main spinner component with variants
2. **LoadingSkeleton** - Content placeholder loading states
3. **Specialized Components** - Pre-configured components for common use cases

### Design Principles

- **Consistency**: All loading states follow the same visual patterns
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels
- **Theme Integration**: Automatic dark/light mode support
- **Performance**: Optimized animations with reduced motion support
- **Flexibility**: Configurable variants for different contexts

## Component Reference

### LoadingSpinner

The core spinner component with multiple variants and configurations.

```tsx
import { LoadingSpinner } from '@/app/components/data-display/loading';

// Basic usage
<LoadingSpinner />

// With message
<LoadingSpinner message="Loading data..." />

// Different variants
<LoadingSpinner variant="accent" size="lg" />

// With progress
<LoadingSpinner 
  progress={75} 
  showProgress 
  message="Processing..." 
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `"default" \| "muted" \| "accent" \| "destructive" \| "success" \| "warning"` | `"default"` | Color variant |
| `size` | `"xs" \| "sm" \| "md" \| "lg" \| "xl" \| "2xl"` | `"md"` | Spinner size |
| `container` | `"inline" \| "block" \| "overlay" \| "card" \| "fullscreen"` | `"inline"` | Container type |
| `message` | `string` | - | Loading message |
| `animate` | `boolean` | `true` | Enable entrance animation |
| `showPulse` | `boolean` | `false` | Show pulse effect |
| `progress` | `number` | - | Progress value (0-100) |
| `showProgress` | `boolean` | `false` | Show progress text |

### Specialized Spinner Components

#### LoadingOverlay
Full-screen or container overlay loading.
```tsx
<LoadingOverlay message="Loading assembly form..." />
```

#### LoadingInline
Inline loading for buttons and small components.
```tsx
<LoadingInline size="sm" variant="muted" />
```

#### LoadingCard
Card-style loading for content areas.
```tsx
<LoadingCard message="Loading data..." />
```

#### SearchLoadingIndicator
Search-specific loading indicator.
```tsx
<SearchLoadingIndicator message="Searching for parts..." />
```

### LoadingSkeleton

Content placeholder component for different layouts.

```tsx
import { LoadingSkeleton } from '@/app/components/data-display/loading';

// Grid skeleton
<LoadingSkeleton variant="grid" count={8} columns={4} />

// List skeleton
<LoadingSkeleton variant="list" count={5} />

// Form skeleton
<LoadingSkeleton variant="form" count={6} showHeader />
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `"default" \| "card" \| "list" \| "grid" \| "form" \| "table"` | `"default"` | Layout variant |
| `count` | `number` | `3` | Number of skeleton items |
| `columns` | `number` | `3` | Grid columns (for grid variant) |
| `showHeader` | `boolean` | `false` | Show header skeleton |
| `showFooter` | `boolean` | `false` | Show footer skeleton |
| `itemHeight` | `string` | - | Custom item height |

### Specialized Skeleton Components

#### PageLoadingSkeleton
Complete page loading with header and content.
```tsx
<PageLoadingSkeleton 
  contentType="grid" 
  showBreadcrumb 
/>
```

#### ChartLoadingSkeleton
Chart/widget loading placeholder.
```tsx
<ChartLoadingSkeleton 
  title="Production Planning"
  height="h-[400px]"
  showLegend 
/>
```

#### FormLoadingSkeleton
Form loading with fields and submit area.
```tsx
<FormLoadingSkeleton 
  fields={5} 
  showSubmit 
/>
```

## Migration Guide

### Replacing Custom Loading Implementations

#### 1. LazyUnifiedAssemblyForm.tsx

**Before:**
```tsx
function LoadingFallback() {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <motion.div className="rounded-lg p-8 shadow-xl bg-gray-900">
        <Loader2 className="h-10 w-10 animate-spin text-primary" />
        <p className="text-lg font-medium">Loading Assembly Form...</p>
      </motion.div>
    </div>
  );
}
```

**After:**
```tsx
import { LoadingOverlay } from '@/app/components/data-display/loading';

const loading = () => <LoadingOverlay message="Loading Assembly Form..." />;
```

#### 2. LazyAssembliesPageWrapper.tsx

**Before:**
```tsx
function LoadingFallback() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="bg-card rounded-lg border p-8 flex justify-center items-center">
        <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading assemblies data...</p>
      </div>
    </div>
  );
}
```

**After:**
```tsx
import { PageLoadingSkeleton } from '@/app/components/data-display/loading';

const loading = () => <PageLoadingSkeleton contentType="grid" />;
```

#### 3. Chart Components

**Before:**
```tsx
function LoadingFallback() {
  return (
    <motion.div className="bg-white dark:bg-card rounded-3xl p-6 shadow-md h-[400px]">
      <div className="flex items-center justify-center h-[300px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Loading production planning data...</p>
      </div>
    </motion.div>
  );
}
```

**After:**
```tsx
import { ChartLoadingSkeleton } from '@/app/components/data-display/loading';

const loading = () => <ChartLoadingSkeleton title="Production Planning" />;
```

#### 4. Search Components

**Before:**
```tsx
{isLoading && (
  <div className="flex items-center justify-center py-6">
    <Loader2 className="h-6 w-6 animate-spin text-yellow-500 mr-2" />
    <span>Searching for parts...</span>
  </div>
)}
```

**After:**
```tsx
import { SearchLoadingIndicator } from '@/app/components/data-display/loading';

{isLoading && <SearchLoadingIndicator message="Searching for parts..." />}
```

## Theme Integration

All loading components automatically integrate with the theme system:

- **Dark/Light Mode**: Automatic color adaptation
- **Theme Variants**: Support for all theme variants (default, blue, green, etc.)
- **Animations**: Consistent with theme animation patterns
- **Accessibility**: Respects `prefers-reduced-motion`

## Accessibility Features

- **ARIA Labels**: Proper labeling for screen readers
- **Focus Management**: Appropriate focus handling
- **Color Contrast**: WCAG 2.1 AA compliant
- **Reduced Motion**: Respects user preferences
- **Semantic HTML**: Proper HTML structure

## Performance Considerations

- **Tree Shaking**: Only used variants included in bundle
- **Lazy Loading**: Framer Motion loaded on demand
- **Memoization**: Optimized re-rendering
- **CSS-in-JS**: Minimal runtime overhead with CVA

## Best Practices

### When to Use Each Component

1. **LoadingOverlay**: Modal/form loading, critical operations
2. **LoadingCard**: Content area loading, data fetching
3. **LoadingInline**: Button states, small components
4. **PageLoadingSkeleton**: Page transitions, initial loads
5. **ChartLoadingSkeleton**: Charts, widgets, visualizations
6. **SearchLoadingIndicator**: Search results, filtering

### Performance Tips

1. Use skeleton loading for content-heavy areas
2. Use spinner loading for quick operations
3. Provide meaningful loading messages
4. Consider progress indicators for long operations
5. Respect reduced motion preferences

### Accessibility Guidelines

1. Always provide descriptive loading messages
2. Use appropriate ARIA labels
3. Ensure sufficient color contrast
4. Test with screen readers
5. Provide alternative text for visual indicators

## Testing

The loading components include comprehensive test coverage:

- Unit tests for all variants
- Accessibility tests
- Theme integration tests
- Animation tests
- Performance tests

## Future Enhancements

- Progress ring indicators
- Skeleton shimmer effects
- Custom loading animations
- Loading state management hooks
- Performance monitoring integration
