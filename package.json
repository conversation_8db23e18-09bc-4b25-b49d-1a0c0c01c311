{"name": "mcp-postman", "version": "1.0.2", "description": "MCP Server for running Postman collections using Newman", "main": "build/index.js", "type": "module", "bin": {"mcp-postman": "./build/index.js"}, "scripts": {"build": "tsc && chmod +x build/index.js", "test": "vitest", "test:ci": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "clean": "rm -rf build", "prepare": "husky", "example:dev": "cross-env NODE_OPTIONS=\"--loader ts-node/esm\" ts-node --watch example/src/server.ts", "example:nodemon": "cross-env NODE_OPTIONS=\"--loader ts-node/esm\"  nodemon --watch example example/src/server.ts", "example:build": "tsc -p example/tsconfig.json"}, "lint-staged": {"*.ts": ["prettier --write", "eslint --fix", "vitest related --run"]}, "keywords": ["mcp", "postman", "newman", "api-testing"], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "latest", "newman": "^6.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/newman": "^5.3.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitest/coverage-v8": "^1.0.0", "axios": "^1.7.9", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "express": "^4.18.2", "husky": "^9.1.7", "lint-staged": "^15.3.0", "nodemon": "3.1.9", "prettier": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}}