{"info": {"name": "Example API Tests", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GET Items", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/items", "host": ["{{baseUrl}}"], "path": ["api", "items"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    const responseData = pm.response.json();", "    pm.expect(Array.isArray(responseData)).to.be.true;", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});"], "type": "text/javascript"}}]}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Item\",\n    \"description\": \"This is a test item\"\n}"}, "url": {"raw": "{{baseUrl}}/api/items", "host": ["{{baseUrl}}"], "path": ["api", "items"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has correct structure', function () {", "    const responseData = pm.response.json();", "    pm.expect(responseData).to.have.property('id');", "    pm.expect(responseData).to.have.property('name');", "    pm.expect(responseData).to.have.property('description');", "    pm.expect(responseData).to.have.property('createdAt');", "});", "", "pm.test('Created item matches request', function () {", "    const responseData = pm.response.json();", "    const requestData = JSON.parse(pm.request.body.raw);", "    pm.expect(responseData.name).to.equal(requestData.name);", "    pm.expect(responseData.description).to.equal(requestData.description);", "});"], "type": "text/javascript"}}]}, {"name": "Get Current Bitcoin Price", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/currentprice", "host": ["{{baseUrl}}"], "path": ["api", "currentprice"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}]}