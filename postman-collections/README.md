# API Testing with Postman & Newman

This project uses Postman collections for API testing, executed through <PERSON> and integrated with Jest.

## Setup

1. Install dependencies: `npm install`
2. Configure environment files in `postman-collections/environments/`

## Running Tests

- All API tests: `npm run test:api`
- Single collection: `npm run test:api:single inventory-transactions`

## Architecture

- Collections stored in `postman-collections/endpoints/`
- Environments in `postman-collections/environments/`
- Common scripts in `postman-collections/common/`
- Jest integration in `__tests__/api/`

## Extending

To add a new API test:
1. Create a new collection in `postman-collections/endpoints/`
2. Follow the standard request/test pattern
3. The test will automatically be included in the full test suite

## MCP Integration

This project includes a Model Context Protocol (MCP) server for Postman collections, allowing AI agents to interact with and run collections:

- Start the MCP server: `npm run mcp-postman`
- Use the MCP client in your code: `const postmanClient = require('./mcp-postman-client')`
- Test the MCP server: `node test-mcp-api.js`
- See `postman-collections/mcp/README.md` for more details 