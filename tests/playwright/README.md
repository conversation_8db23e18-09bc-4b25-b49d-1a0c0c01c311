# Assembly CRUD Operations - Playwright Tests

This directory contains Playwright tests for automating the testing of Assembly CRUD operations in the Trend IMS application.

## Test Files

- `assembly-crud.test.js` - Combined tests for all CRUD operations
- `create-assembly.test.js` - Test for creating a new assembly
- `update-assembly.test.js` - Test for updating an existing assembly
- `delete-assembly.test.js` - Test for deleting an assembly
- `view-assembly.test.js` - Test for viewing assembly details

## Running Tests

Ensure the application is running on port 5174 or update the port in the test files and the `playwright.config.js` file.

### Run Individual Tests

```bash
# Run the create assembly test
npm run test:create-assembly

# Run the update assembly test
npm run test:update-assembly

# Run the delete assembly test
npm run test:delete-assembly

# Run the view assembly test
npm run test:view-assembly
```

### Run All Tests

```bash
# Run all assembly tests
npm run test:assemblies:all

# Run the combined assembly-crud test
npm run test:assemblies:playwright
```

## Test Results

Screenshots are saved in the `tests/playwright/screenshots` directory and are taken at various points during the tests.

## Configuration

Configuration for the Playwright tests is defined in `playwright.config.js` in the root directory. This includes settings like:

- Base URL (http://localhost:5174)
- <PERSON><PERSON><PERSON> to use (Chromium)
- Screenshot settings
- Timeout settings

## Prerequisites

- Node.js installed
- Dependencies installed (`npm install`)
- Playwright browsers installed (`npx playwright install`)

## Test Strategy

The tests use a robust approach to handle potential failures:

1. Each test captures screenshots at key points
2. Tests verify both UI elements and data consistency
3. Error handling for cases where elements might not be found
4. Proper cleanup to ensure test isolation 