# Trend IMS API Testing with mcp-postman

This folder contains Postman collections and environments for testing the Trend IMS APIs using the mcp-postman tool.

## File Structure

- `collections/`: Contains Postman collections
  - `sample-collection.json`: A simple sample collection for testing the mcp-postman setup
  - `api-tests.json`: Comprehensive API tests for the Trend IMS system
- `environments/`: Contains Postman environments
  - `sample-environment.json`: A simple sample environment for the sample collection
  - `ims-api-environment.json`: Environment for testing the Trend IMS APIs
- `run-test.js`: Script to run the sample collection using Newman directly
- `run-mcp-test.js`: Script to run the sample collection using mcp-postman
- `run-api-tests.js`: Script to run the Trend IMS API tests using mcp-postman

## Prerequisites

- Node.js installed
- The Trend IMS server running locally
- Newman installed globally (`npm install -g newman`)
- mcp-postman server set up in the parent directory (`../mcp-postman`)

## Running the API Tests

### Using the Script

To run the API tests using the provided script:

```bash
node run-api-tests.js
```

This will execute the API tests using the mcp-postman server and display a summary of the results.

### Using Cursor with mcp-postman

To run the API tests using Cursor with mcp-postman integration:

1. Make sure the mcp-postman server is configured in your `.cursor/mcp.json` file
2. In Cursor, use the following prompt:

```
Run the Postman collection at /Users/<USER>/Cursor/Trend_IMS/postman-test/collections/api-tests.json with the environment file at /Users/<USER>/Cursor/Trend_IMS/postman-test/environments/ims-api-environment.json and tell me if all the APIs are working.
```

## Test Coverage

The API tests cover the following endpoints:

- **System Status**
  - `/api/status`: Checks if the API is online
  - `/api/db-status`: Checks if the database is connected

- **Parts API**
  - `/api/parts`: GET - Retrieves all parts
  - `/api/parts`: POST - Creates a new part
  - `/api/parts/:id`: GET - Retrieves a specific part

- **Inventory API**
  - `/api/inventory`: GET - Retrieves inventory information

- **Suppliers API**
  - `/api/suppliers`: GET - Retrieves all suppliers

- **Products API**
  - `/api/products`: GET - Retrieves all products

- **Assemblies API**
  - `/api/assemblies`: GET - Retrieves all assemblies

- **Categories API**
  - `/api/categories`: GET - Retrieves all categories

- **Warehouses API**
  - `/api/warehouses`: GET - Retrieves all warehouses

## Extending the Tests

To add more tests:

1. Edit the `collections/api-tests.json` file to add new requests and tests
2. Update the `environments/ims-api-environment.json` file if you need new environment variables 