### MongoDB Schema Definition (IMS Database) - Updated

This schema is derived from the actual database structure as extracted from the MongoDB database.

---

**1. `parts` Collection**

* `_id`: String (Primary Key - Part Number/Code)
* `name`: String (Name of the part)
* `description`: String (Optional description)
* `technical_specs`: String (Optional technical specifications)
* `is_manufactured`: Boolean (Indicates if the part is manufactured in-house)
* `reorder_level`: Int32 | Null (Stock level at which reordering is triggered)
* `status`: String (e.g., "active", "inactive", "obsolete")
* `inventory`: Object
    * Contains information about current stock, location, etc.

---

**2. `warehouses` Collection**

* `_id`: ObjectId (Primary Key)
* `location_id`: String (Unique identifier for the location)
* `name`: String (Human-readable name of the warehouse/location)
* `location`: String (Descriptive location details)
* `capacity`: Int32 (Storage capacity)
* `manager`: String (Name of the manager responsible)
* `contact`: String (Contact information)
* `createdAt`: Date (Timestamp of warehouse record creation)

---

**3. `suppliers` Collection**

* `_id`: ObjectId (Primary Key)
* `supplier_id`: String (Unique identifier for the supplier)
* `name`: String (Name of the supplier company)
* `contactPerson`: String (Name of the primary contact)
* `email`: String (Supplier contact email)
* `phone`: String (Supplier contact phone number)
* `address`: String (Supplier's physical address)
* `specialty`: Array<String> (List of product types the supplier specializes in)
* `rating`: Double | Int32 (Supplier performance rating)
* `createdAt`: Date (Timestamp of supplier record creation)

---

**4. `users` Collection**

* `_id`: ObjectId (Primary Key)
* `username`: String (Unique login username)
* `email`: String (User's email address)
* `fullName`: String (User's full name)
* `role`: String (User role)
* `passwordHash`: String (Securely hashed password)
* `isActive`: Boolean (Indicates if the user account is active)
* `createdAt`: Date (Timestamp of user record creation)

---

**5. `categories` Collection**

* `_id`: ObjectId (Primary Key)
* `name`: String (Name of the category)
* `description`: String (Optional description of the category)
* `parentCategory`: ObjectId | Null (Reference to parent category)
* `createdAt`: Date (Timestamp of category record creation)

---

**6. `products` Collection**

* `_id`: ObjectId (Primary Key)
* `productCode`: String (Unique code for the product)
* `name`: String (Name of the product)
* `description`: String (Description of the product)
* `categoryId`: ObjectId (Reference to category)
* `status`: String (e.g., "active", "discontinued")
* `sellingPrice`: Int32 (Price of the product)
* `createdAt`: Date (Timestamp of product record creation)

---

**7. `assemblies` Collection**

* `_id`: ObjectId (Primary Key)
* `assemblyCode`: String (Unique code for the assembly)
* `name`: String (Name of the assembly)
* `productId`: ObjectId | Null (Reference to related product)
* `parentId`: Null | ObjectId (Reference to parent assembly if this is a sub-assembly)
* `isTopLevel`: Boolean (Indicates if this is a top-level assembly)
* `partsRequired`: Array (Parts required for this assembly)
* `status`: String (Assembly status)
* `createdAt`: Date (Timestamp of creation)

---

**8. `transactions` Collection**

* `_id`: ObjectId (Primary Key)
* `partId`: String (Reference to related part)
* `transactionType`: String (Type of transaction)
* `quantity`: Int32 (Quantity involved)
* `previousStock`: Int32 (Stock before transaction)
* `newStock`: Int32 (Stock after transaction)
* `transactionDate`: Date (Date of transaction)
* `referenceNumber`: String (Reference to related documents)
* `notes`: String (Additional notes)
* `originalSqlId`: Null (Legacy system reference)
* `createdAt`: Date (Timestamp of creation)

---

**9. `purchaseorders` Collection**

* `_id`: ObjectId (Primary Key)
* `poNumber`: String (Purchase order number)
* `supplierId`: ObjectId (Reference to supplier)
* `orderDate`: Date (Date the order was placed)
* `expectedDeliveryDate`: Date (Expected delivery date)
* `items`: Array (Items included in the order)
* `totalAmount`: Int32 (Total cost of the order)
* `status`: String (Order status)
* `notes`: String (Additional notes)
* `createdBy`: ObjectId (User who created the PO)
* `createdAt`: Date (Timestamp of creation)

---

**10. `workorders` Collection**

* `_id`: ObjectId (Primary Key)
* `woNumber`: String (Work order number)
* `assemblyId`: ObjectId | Null (Reference to assembly)
* `partIdToManufacture`: String (Part to be manufactured)
* `productId`: ObjectId | Null (Reference to product)
* `quantity`: Int32 (Quantity to produce)
* `status`: String (Order status)
* `priority`: String (Priority level)
* `assignedTo`: ObjectId (User assigned to the work order)
* `completedAt`: Date (Completion timestamp)
* `createdAt`: Date (Creation timestamp)

---

**11. `batches` Collection**

* `_id`: ObjectId (Primary Key)
* `batchCode`: String (Unique code for the batch)
* `partId`: String | Null (Reference to part being produced)
* `assemblyId`: ObjectId | Null (Reference to assembly being produced)
* `quantityPlanned`: Int32 (Planned quantity)
* `quantityProduced`: Int32 (Actual quantity produced)
* `startDate`: Date (Production start date)
* `endDate`: Date (Production end date)
* `status`: String (Batch status)
* `notes`: String (Additional notes)
* `workOrderId`: ObjectId (Reference to related work order)

---

**12. `batchlogs` Collection**

* `_id`: ObjectId (Primary Key)
* `batchId`: ObjectId (Reference to batch)
* `timestamp`: Date (Time of the log entry)
* `event`: String (Description of the event)
* `userId`: ObjectId (User who logged the event)
* `details`: String (Additional details)

---

**13. `deliveries` Collection**

* `_id`: ObjectId (Primary Key)
* `deliveryId`: String (Unique delivery identifier)
* `referenceType`: String (Type of reference)
* `referenceId`: ObjectId (Reference to related document)
* `supplierId`: ObjectId (Reference to supplier)
* `status`: String (Delivery status)
* `scheduledDate`: Date (Scheduled delivery date)
* `actualDate`: Date | Null (Actual delivery date)
* `trackingNumber`: String (Delivery tracking number)
* `notes`: String (Additional notes)
* `receivedBy`: ObjectId | Null (User who received the delivery)
* `createdAt`: Date (Creation timestamp)

---

**14. `settings` Collection**

* `_id`: ObjectId (Primary Key)
* `key`: String (Setting key)
* `value`: String (Setting value)
* `description`: String (Description of the setting)
* `dataType`: String (Data type of the value)
* `group`: String (Setting group)
* `lastModifiedBy`: ObjectId (User who last modified the setting)
* `lastModifiedAt`: Date (Last modification timestamp)

---

**15. `IMS-TEJ` Collection** (System Logs)

* `_id`: ObjectId (Primary Key)
* `timestamp`: Date (Log timestamp)
* `eventType`: String (Type of event)
* `level`: String (Log level)
* `message`: String (Log message)
* `source`: String (Source of the log)
* `details`: Object (Additional details)

---

## Schema Design Recommendations

Based on MongoDB best practices:

1. **Embedded Documents**: The schema makes good use of embedded documents (e.g., `inventory` in `parts`, `items` in `purchaseorders`), which reduces the need for joins.

2. **References**: References between collections are appropriately used for many-to-many relationships.

3. **Schema Versioning**: Consider adding a `schemaVersion` field to documents to handle future schema changes.

4. **Indexing**: Ensure proper indexes are created for frequently queried fields, especially those used for references.

5. **Duplication Strategy**: The current schema has a good balance between normalization and denormalization. Consider duplicating small amounts of frequently accessed data to reduce lookups. 