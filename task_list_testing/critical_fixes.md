# TrendTech IMS - Critical Issue Fixes

## Connection Timeout (IMS-TEJ-1N)

### Problem
Connection timeout after 30000ms indicates database connection issues.

### Suggested Fix
```javascript
// In database connection configuration
const mongoose = require('mongoose');

// Improve connection options
const connectionOptions = {
  connectTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  serverSelectionTimeoutMS: 30000,
  heartbeatFrequencyMS: 10000,
  maxPoolSize: 10,
  minPoolSize: 2,
  retryWrites: true,
  retryReads: true,
};

// Add connection retry logic
const connectWithRetry = async (uri, options, maxRetries = 5, delay = 5000) => {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      await mongoose.connect(uri, options);
      console.log('MongoDB connection successful');
      return;
    } catch (err) {
      retries += 1;
      console.log(`MongoDB connection attempt ${retries} failed: ${err.message}`);
      console.log(`Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error(`Failed to connect to MongoDB after ${maxRetries} attempts`);
};

// Usage
connectWithRetry(process.env.MONGODB_URI, connectionOptions)
  .catch(err => {
    console.error('Fatal database connection error:', err);
    process.exit(1);
  });
```

## GET /api/assemblies Error (IMS-TEJ-Z)

### Problem
The GET /api/assemblies endpoint is failing without proper error handling.

### Suggested Fix
```javascript
// In assemblies route handler
router.get('/api/assemblies', async (req, res) => {
  try {
    // Add proper pagination to avoid large result sets
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    
    // Add proper error logging
    console.log(`Fetching assemblies with pagination: page=${page}, limit=${limit}`);
    
    // Add query timeout
    const assemblies = await Assembly.find({})
      .populate('category')
      .limit(limit)
      .skip(skip)
      .lean()
      .timeout(10000); // 10 second timeout for query
    
    // Return proper pagination metadata
    const total = await Assembly.countDocuments({});
    
    return res.status(200).json({
      success: true,
      data: assemblies,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    // Detailed error logging
    console.error(`Assembly fetch failed: ${err.message}`, {
      stack: err.stack,
      query: req.query
    });
    
    // Handle specific error types
    if (err.name === 'MongoServerError') {
      return res.status(500).json({
        success: false,
        error: 'Database error occurred',
        code: 'DB_ERROR'
      });
    }
    
    if (err.name === 'TimeoutError') {
      return res.status(408).json({
        success: false,
        error: 'Request timed out',
        code: 'TIMEOUT'
      });
    }
    
    // Generic error response
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch assemblies',
      code: 'SERVER_ERROR'
    });
  }
});
```

## Invalid Part ID Format Error (IMS-TEJ-27/26)

### Problem
Assembly creation fails when part IDs contain a dot notation format (e.g., "DL23.108").

### Suggested Fix
```javascript
// In assembly service validation function (likely in app/services/assembly.service.ts)
// Update the part ID validation regex to allow dot notation

// Current restrictive regex (likely causes the issue)
// const partIdRegex = /^[A-Z0-9\-]+$/;  // Only allows letters, numbers, and hyphens

// Updated more permissive regex that allows dots
const partIdRegex = /^[A-Z0-9\-\.]+$/i;  // Allows letters, numbers, hyphens, and dots (case insensitive)

// Example validation function
const validatePartId = (partId) => {
  if (!partId || typeof partId !== 'string') {
    return false;
  }
  
  return partIdRegex.test(partId);
};

// In the assembly creation function
const createAssembly = async (assemblyData) => {
  try {
    // Validate all part IDs in the assembly
    for (const part of assemblyData.partsRequired || []) {
      if (!validatePartId(part.partId)) {
        throw new Error(`Invalid part ID format for part: ${part.partId}`);
      }
      
      // Also validate children if they exist
      if (part.children && Array.isArray(part.children)) {
        for (const childPart of part.children) {
          if (!validatePartId(childPart.partId)) {
            throw new Error(`Invalid part ID format for child part: ${childPart.partId}`);
          }
        }
      }
    }
    
    // Proceed with assembly creation if all part IDs are valid
    // ...existing assembly creation code
    
  } catch (err) {
    console.error(`Assembly creation failed: ${err.message}`, {
      stack: err.stack,
      assemblyCode: assemblyData.assemblyCode
    });
    throw err;
  }
};
```

## Cannot Read Properties of Undefined (IMS-TEJ-10)

### Problem
The inventory table component is trying to access the length property of an undefined or null inventoryItems array.

### Suggested Fix
```jsx
// In the InventoryTable component
const InventoryTable = ({ inventoryItems }) => {
  // Add null safety check before accessing length
  const hasItems = inventoryItems && Array.isArray(inventoryItems) && inventoryItems.length > 0;
  
  return (
    <div className="inventory-table-container">
      {!hasItems ? (
        <div className="no-items-message">
          <p>No inventory items found.</p>
        </div>
      ) : (
        <table className="inventory-table">
          <thead>
            <tr>
              <th>Part Number</th>
              <th>Name</th>
              <th>Quantity</th>
              {/* Other columns */}
            </tr>
          </thead>
          <tbody>
            {inventoryItems.map((item) => (
              <tr key={item.id || item._id}>
                <td>{item.partNumber}</td>
                <td>{item.name}</td>
                <td>{item.quantity || 0}</td>
                {/* Other cells */}
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

// With default props for additional safety
InventoryTable.defaultProps = {
  inventoryItems: [] // Provide a default empty array
};

export default InventoryTable;
```

## StrictPopulateError in Purchase Orders (IMS-TEJ-1R)

### Problem
Schema does not allow population of items.part_id path.

### Suggested Fix
```javascript
// In purchase order schema
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Updated schema with proper population paths
const purchaseOrderItemSchema = new Schema({
  part_id: {
    type: Schema.Types.ObjectId,
    ref: 'Part', // Ensure this matches your part model name
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  // Other fields...
});

const purchaseOrderSchema = new Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  supplier: {
    type: Schema.Types.ObjectId,
    ref: 'Supplier',
    required: true
  },
  items: [purchaseOrderItemSchema],
  // Other fields...
}, {
  // Important: Allow population of paths not in schema
  strictPopulate: false,
  timestamps: true
});

// Fix for the GET endpoint
router.get('/api/purchase-orders', async (req, res) => {
  try {
    const orders = await PurchaseOrder.find({})
      .populate({
        path: 'items.part_id',
        model: 'Part'
      })
      .populate('supplier')
      .lean();
      
    return res.status(200).json({
      success: true,
      data: orders
    });
  } catch (err) {
    console.error(`Failed to fetch purchase orders: ${err.message}`, {
      stack: err.stack
    });
    
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch purchase orders',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});
```

These fixes address the highest priority issues in the system. After implementing them, additional tests should be created to verify they resolve the reported Sentry errors. 