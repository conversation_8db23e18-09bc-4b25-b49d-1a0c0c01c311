# Data Loading and Connection Improvements

## Overview
This document outlines the implementation strategy for improving data loading and connection handling in the Trend_IMS inventory management system. These improvements will enhance reliability, error handling, and overall performance of inventory operations.

## Current Connection Issues
- Intermittent connection failures to MongoDB
- Lack of proper error handling for API failures
- Unoptimized database queries causing slow inventory listing
- Missing validation for inventory input parameters
- No visibility into connection status for troubleshooting

## Implementation Strategy

### 1. Robust Connection Handling

```typescript
// app/lib/mongodb.ts
import { MongoClient, Db, MongoClientOptions } from 'mongodb';

class MongoDBManager {
  private static instance: MongoDBManager;
  private client: MongoClient | null = null;
  private dbName: string;
  private connectionPromise: Promise<MongoClient> | null = null;
  private connectionAttempts: number = 0;
  private readonly maxConnectionAttempts: number = 5;
  private readonly connectionRetryDelay: number = 2000; // ms
  
  private constructor() {
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    this.dbName = process.env.MONGODB_DB_NAME || 'trend_ims';
    
    const options: MongoClientOptions = {
      connectTimeoutMS: 5000,
      socketTimeoutMS: 30000,
      maxPoolSize: 50,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      waitQueueTimeoutMS: 5000,
    };
    
    this.client = new MongoClient(uri, options);
  }
  
  public static getInstance(): MongoDBManager {
    if (!MongoDBManager.instance) {
      MongoDBManager.instance = new MongoDBManager();
    }
    return MongoDBManager.instance;
  }
  
  public async connect(): Promise<MongoClient> {
    if (this.client?.topology?.isConnected()) {
      return this.client;
    }
    
    if (this.connectionPromise) {
      return this.connectionPromise;
    }
    
    this.connectionAttempts = 0;
    return this.attemptConnection();
  }
  
  private async attemptConnection(): Promise<MongoClient> {
    this.connectionPromise = new Promise(async (resolve, reject) => {
      try {
        this.connectionAttempts++;
        console.log(`Connecting to MongoDB (Attempt ${this.connectionAttempts}/${this.maxConnectionAttempts})...`);
        
        await this.client!.connect();
        console.log('Successfully connected to MongoDB');
        
        // Set up connection monitoring
        this.client!.on('close', this.handleDisconnect.bind(this));
        this.client!.on('error', this.handleError.bind(this));
        
        resolve(this.client!);
      } catch (error) {
        console.error('MongoDB connection error:', error);
        
        if (this.connectionAttempts < this.maxConnectionAttempts) {
          console.log(`Retrying connection in ${this.connectionRetryDelay}ms...`);
          setTimeout(() => {
            this.connectionPromise = null;
            resolve(this.attemptConnection());
          }, this.connectionRetryDelay);
        } else {
          console.error(`Failed to connect after ${this.maxConnectionAttempts} attempts`);
          this.connectionPromise = null;
          reject(error);
        }
      }
    });
    
    return this.connectionPromise;
  }
  
  private handleDisconnect() {
    console.warn('MongoDB disconnected. Attempting to reconnect...');
    this.connectionPromise = null;
    // Trigger reconnection on next database operation
  }
  
  private handleError(error: Error) {
    console.error('MongoDB connection error:', error);
    // Log to monitoring system
  }
  
  public async getDb(): Promise<Db> {
    const client = await this.connect();
    return client.db(this.dbName);
  }
  
  public async withDatabase<T>(operation: (db: Db) => Promise<T>): Promise<T> {
    try {
      const db = await this.getDb();
      return await operation(db);
    } catch (error) {
      console.error('Database operation error:', error);
      throw error;
    }
  }
  
  public async close(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.connectionPromise = null;
      console.log('MongoDB connection closed');
    }
  }
  
  public getConnectionStatus(): {
    connected: boolean;
    connectionAttempts: number;
    poolSize?: number;
    databaseName: string;
  } {
    const connected = this.client?.topology?.isConnected() || false;
    const poolSize = this.client?.topology?.connections?.size;
    
    return {
      connected,
      connectionAttempts: this.connectionAttempts,
      poolSize,
      databaseName: this.dbName
    };
  }
}

export default MongoDBManager.getInstance();
```

### 2. Comprehensive Error Handling for API Routes

```typescript
// app/lib/error-handler.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { NextRequest, NextResponse } from 'next/server';

export class ApiError extends Error {
  statusCode: number;
  errorCode: string;
  
  constructor(message: string, statusCode: number = 500, errorCode: string = 'INTERNAL_SERVER_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.name = 'ApiError';
  }
}

// Error handler for Pages Router
export function withErrorHandling(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      await handler(req, res);
    } catch (error) {
      console.error('API Error:', error);
      
      // Log to monitoring service
      // logToMonitoring(error, req);
      
      if (error instanceof ApiError) {
        return res.status(error.statusCode).json({
          error: {
            message: error.message,
            code: error.errorCode
          }
        });
      }
      
      // Handle MongoDB specific errors
      if (error.name === 'MongoServerError') {
        if (error.code === 11000) {
          return res.status(409).json({
            error: {
              message: 'A duplicate record already exists',
              code: 'DUPLICATE_RECORD'
            }
          });
        }
      }
      
      // Default error response
      return res.status(500).json({
        error: {
          message: 'An unexpected error occurred',
          code: 'INTERNAL_SERVER_ERROR'
        }
      });
    }
  };
}

// Error handler for App Router
export function withErrorHandlingAppRouter(
  handler: (request: Request) => Promise<Response>
) {
  return async (request: Request) => {
    try {
      return await handler(request);
    } catch (error) {
      console.error('API Error:', error);
      
      // Log to monitoring service
      // logToMonitoring(error, request);
      
      if (error instanceof ApiError) {
        return NextResponse.json(
          {
            error: {
              message: error.message,
              code: error.errorCode
            }
          },
          { status: error.statusCode }
        );
      }
      
      // Handle MongoDB specific errors
      if (error.name === 'MongoServerError') {
        if (error.code === 11000) {
          return NextResponse.json(
            {
              error: {
                message: 'A duplicate record already exists',
                code: 'DUPLICATE_RECORD'
              }
            },
            { status: 409 }
          );
        }
      }
      
      // Default error response
      return NextResponse.json(
        {
          error: {
            message: 'An unexpected error occurred',
            code: 'INTERNAL_SERVER_ERROR'
          }
        },
        { status: 500 }
      );
    }
  };
}
```

### 3. Optimized Database Queries

```typescript
// app/api/inventory/route.ts
import { NextResponse } from 'next/server';
import mongoDBManager from '@/lib/mongodb';
import { withErrorHandlingAppRouter } from '@/lib/error-handler';
import { ObjectId } from 'mongodb';

// Optimized inventory listing with projection and indexing
async function getInventoryItems(request: Request) {
  const { searchParams } = new URL(request.url);
  
  // Parse query parameters
  const page = parseInt(searchParams.get('page') || '1', 10);
  const limit = parseInt(searchParams.get('limit') || '20', 10);
  const sortField = searchParams.get('sortBy') || 'name';
  const sortOrder = searchParams.get('sortOrder') === 'desc' ? -1 : 1;
  const category = searchParams.get('category');
  const searchTerm = searchParams.get('search');
  const minStock = searchParams.get('minStock') ? parseInt(searchParams.get('minStock')!, 10) : undefined;
  const maxStock = searchParams.get('maxStock') ? parseInt(searchParams.get('maxStock')!, 10) : undefined;
  
  // Build query
  const query: any = {};
  
  if (category) {
    query.categoryId = category;
  }
  
  if (searchTerm) {
    // Use text index if available, otherwise use regex
    query.$or = [
      { name: { $regex: searchTerm, $options: 'i' } },
      { sku: { $regex: searchTerm, $options: 'i' } },
      { description: { $regex: searchTerm, $options: 'i' } }
    ];
  }
  
  if (minStock !== undefined || maxStock !== undefined) {
    query.quantity = {};
    if (minStock !== undefined) query.quantity.$gte = minStock;
    if (maxStock !== undefined) query.quantity.$lte = maxStock;
  }
  
  // Calculate pagination
  const skip = (page - 1) * limit;
  
  return await mongoDBManager.withDatabase(async (db) => {
    const collection = db.collection('inventory_items');
    
    // Create indexes if they don't exist (in production, create indexes during deployment)
    if (process.env.NODE_ENV === 'development') {
      await collection.createIndexes([
        { key: { name: 1 } },
        { key: { sku: 1 }, unique: true },
        { key: { categoryId: 1 } },
        { key: { quantity: 1 } },
        { key: { name: 'text', description: 'text' } }
      ]);
    }
    
    // Execute count query with timeout
    const countPromise = collection.countDocuments(query, { maxTimeMS: 5000 });
    
    // Execute find query with projection, sorting, and timeout
    const itemsPromise = collection.find(query, {
      // Project only needed fields
      projection: {
        name: 1,
        sku: 1,
        categoryId: 1,
        quantity: 1,
        reorderLevel: 1,
        sellingPrice: 1,
        images: { $slice: 1 }, // Only get first image
        isActive: 1,
        updatedAt: 1
      },
      sort: { [sortField]: sortOrder },
      skip,
      limit,
      maxTimeMS: 5000
    }).toArray();
    
    // Execute both queries in parallel
    const [totalItems, items] = await Promise.all([countPromise, itemsPromise]);
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(totalItems / limit);
    
    return NextResponse.json({
      items,
      pagination: {
        page,
        limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  });
}

export const GET = withErrorHandlingAppRouter(getInventoryItems);
```

### 4. Data Validation for Inventory Input

```typescript
// app/lib/validators/inventory.ts
import { z } from 'zod';

// Base schema for inventory item validation
export const inventoryItemSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(100, 'Product name is too long'),
  sku: z.string().min(1, 'SKU is required').max(50, 'SKU is too long'),
  description: z.string().optional(),
  categoryId: z.string().min(1, 'Category is required'),
  supplierId: z.string().optional(),
  quantity: z.number().int().min(0, 'Quantity cannot be negative'),
  reorderLevel: z.number().int().min(0, 'Reorder level cannot be negative'),
  costPrice: z.number().min(0, 'Cost price cannot be negative'),
  sellingPrice: z.number().min(0, 'Selling price cannot be negative'),
  attributes: z.array(
    z.object({
      key: z.string().min(1, 'Attribute key is required'),
      value: z.string().min(1, 'Attribute value is required')
    })
  ).optional(),
  images: z.array(z.string().url('Invalid image URL')).optional(),
  isActive: z.boolean().default(true)
});

// Schema for creating a new inventory item
export const createInventoryItemSchema = inventoryItemSchema.omit({
  _id: true
});

// Schema for updating an existing inventory item
export const updateInventoryItemSchema = inventoryItemSchema.partial().omit({
  _id: true
});

// Schema for inventory item filters
export const inventoryFilterSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  category: z.string().optional(),
  search: z.string().optional(),
  minStock: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
  maxStock: z.string().optional().transform(val => val ? parseInt(val, 10) : undefined),
});

// Validation function for inventory item creation
export function validateInventoryItem(data: unknown) {
  return createInventoryItemSchema.safeParse(data);
}

// Validation function for inventory item update
export function validateInventoryItemUpdate(data: unknown) {
  return updateInventoryItemSchema.safeParse(data);
}

// Validation function for inventory filters
export function validateInventoryFilters(data: unknown) {
  return inventoryFilterSchema.safeParse(data);
}
```

### 5. Connection Status Monitoring

```typescript
// app/api/system/status/route.ts
import { NextResponse } from 'next/server';
import mongoDBManager from '@/lib/mongodb';
import { withErrorHandlingAppRouter } from '@/lib/error-handler';

async function getSystemStatus(request: Request) {
  // Get MongoDB connection status
  const mongoStatus = mongoDBManager.getConnectionStatus();
  
  // Get system information
  const systemInfo = {
    environment: process.env.NODE_ENV || 'development',
    version: process.env.APP_VERSION || '1.0.0',
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };
  
  // Check database connectivity
  let databaseConnected = false;
  try {
    await mongoDBManager.withDatabase(async (db) => {
      // Simple ping to verify connection
      await db.command({ ping: 1 });
      databaseConnected = true;
    });
  } catch (error) {
    console.error('Database connectivity check failed:', error);
  }
  
  return NextResponse.json({
    status: databaseConnected ? 'healthy' : 'degraded',
    database: {
      ...mongoStatus,
      connected: databaseConnected
    },
    system: systemInfo
  });
}

export const GET = withErrorHandlingAppRouter(getSystemStatus);
```

## Implementation Roadmap

### Phase 1: Connection Handling and Error Management
1. Implement robust MongoDB connection manager
2. Create comprehensive error handling middleware
3. Set up connection status monitoring endpoint
4. Add logging for connection events and errors

### Phase 2: Query Optimization and Validation
1. Optimize database queries with proper indexing
2. Implement data validation for all inventory inputs
3. Add query timeout handling and fallback mechanisms
4. Create database operation retry logic

### Phase 3: Testing and Monitoring
1. Implement connection resilience tests
2. Create load testing scenarios for database operations
3. Set up monitoring dashboards for connection status
4. Document error handling patterns for developers

## Success Metrics

- **Connection Reliability**: Reduce connection failures by 95%
- **Error Handling Coverage**: Achieve 100% of API routes with proper error handling
- **Query Performance**: Reduce average query time by 60%
- **Data Validation**: Eliminate invalid data submissions
- **System Visibility**: Provide real-time connection status monitoring

## Conclusion

This data loading and connection improvements plan provides a comprehensive approach to enhancing the reliability and performance of the Trend_IMS inventory management system. By implementing robust connection handling, comprehensive error management, optimized queries, and proper data validation, the system will be more resilient, performant, and maintainable.