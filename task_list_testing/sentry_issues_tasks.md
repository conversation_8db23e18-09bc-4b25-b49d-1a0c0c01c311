# TrendTech IMS - Sentry Issues Task List

## API Issues

### IMS-TEJ-Z - GET /api/assemblies Error
- [x] Investigate why GET /api/assemblies is failing
- [x] Review route handler for proper error handling
- [x] Add logging to identify specific failure conditions
- [x] Fix endpoint implementation
- [x] Add tests to verify fix

### IMS-TEJ-23 - Duplicate Assembly Code Error
- [x] Fix validation in POST /api/assemblies to properly check for existing codes
- [x] Implement proper error response for duplicate assembly codes
- [x] Add unique constraint in database schema for assembly codes
- [x] Add frontend validation for assembly code input
- [x] Update documentation about unique assembly codes

### IMS-TEJ-1T - GET /api/assemblies/[id] Error
- [x] Debug GET /api/assemblies/ASM-SQZ-SML endpoint (enhanced logging added)
- [x] Review parameter validation for assembly ID (validation exists in route)
- [x] Check population logic for related data (parts, category, etc.) (reviewed, added logging)
- [x] Ensure proper error handling for not found and invalid IDs (handling exists, improved logging)
- [x] Add tests for single assembly retrieval

### IMS-TEJ-1Z - GET /api/assemblies/[id] with MongoDB ObjectId
- [ ] Identify why ObjectId-based lookups are failing (65f000030000000000000001)
- [ ] Add validation to handle both string IDs and ObjectIDs
- [ ] Implement consistent ID lookup logic across the API
- [ ] Add tests for different ID format lookups
- [ ] Update documentation about acceptable ID formats

### IMS-TEJ-1Y - GET /api/assemblies/[id] - ASM-TA-100
- [ ] Debug GET /api/assemblies/ASM-TA-100 endpoint
- [ ] Verify assembly with code ASM-TA-100 exists in the database
- [ ] Add better error handling for non-existent assemblies
- [ ] Log assembly lookup attempts to aid debugging
- [ ] Add tests for error scenarios

### IMS-TEJ-25 - DELETE /api/assemblies/[id]
- [ ] Debug DELETE /api/assemblies/681f796bd6a21248b8ec7640 endpoint
- [ ] Check if assembly exists before deletion attempt
- [ ] Improve error handling for deletion failures
- [ ] Implement proper cascading deletion for dependent records
- [ ] Add tests for deletion operations

### IMS-TEJ-27/26 - Invalid part ID format error
- [ ] Fix validation in POST /api/assemblies to handle part IDs with dot notation (e.g., "DL23.108")
- [ ] Update part ID format validation regex to be more inclusive
- [ ] Add proper error handling with clear messages for invalid formats
- [ ] Test assembly creation with various part ID formats
- [ ] Update documentation about acceptable part ID formats

### IMS-TEJ-1W - POST /api/assemblies Error
- [ ] Debug general errors with POST /api/assemblies endpoint
- [ ] Implement comprehensive input validation
- [ ] Add better error messaging and logging
- [ ] Test assembly creation with various data sets
- [ ] Document common error cases and solutions

### IMS-TEJ-24 - adaptProductsForTable is not defined
- [ ] Debug inventory page to identify missing function
- [ ] Implement adaptProductsForTable function or fix import statement
- [ ] Add appropriate data transformation for inventory table
- [ ] Test inventory page functionality
- [ ] Add error handling for table data preparation

### IMS-TEJ-V - includeParts is not defined
- [ ] Debug GET /api/users endpoint to identify missing includeParts function
- [ ] Implement or fix import for includeParts function
- [ ] Review user data retrieval logic
- [ ] Add error handling for optional parameters
- [ ] Test user data retrieval

### IMS-TEJ-10 - Cannot read properties of undefined (reading 'length')
- [ ] Fix inventory table component to handle undefined or null inventoryItems
- [ ] Add null checking before accessing length property
- [ ] Implement default empty array for inventory data
- [ ] Test inventory page with various data states
- [ ] Add loading state for data fetching

### IMS-TEJ-14 - ValidationError in POST /api/parts
- [ ] Fix part creation to handle UUID properly or convert to ObjectId
- [ ] Add validation for required fields (partNumber, inventory.abcClassification, inventory.warehouseId)
- [ ] Update part schema validation
- [ ] Improve error messaging for missing required fields
- [ ] Test part creation with various input data

### IMS-TEJ-H - GET /api/analytics Error
- [ ] Debug errors with GET /api/analytics endpoint
- [ ] Review analytics data aggregation logic
- [ ] Add proper error handling and timeouts
- [ ] Implement performance optimizations for analytics queries
- [ ] Add tests for analytics data retrieval

### IMS-TEJ-13 - CastError for RegExp in Parts Search
- [ ] Investigate CastError when using RegExp in GET /api/parts/search
- [ ] Ensure search query containing RegExp (e.g., "/sque/i") is handled correctly
- [ ] Validate and sanitize search inputs to prevent invalid ObjectId casts
- [ ] Improve error handling for search queries with special characters or patterns
- [ ] Add tests for RegExp and special character searches

### IMS-TEJ-S - Error in Inventory Reports API
- [ ] Debug errors with GET /api/reports/inventory endpoint
- [ ] Review inventory report generation logic
- [ ] Ensure all data dependencies for reports are correctly fetched and handled
- [ ] Add specific error logging for report generation failures
- [ ] Test inventory report generation with various data scenarios

### IMS-TEJ-16 - Issues with Products API
- [ ] Debug errors in GET /api/products endpoint
- [ ] Review product data retrieval and serialization logic
- [ ] Ensure database queries for products are optimized
- [ ] Add comprehensive error handling for product API
- [ ] Test product listing and individual product retrieval

### IMS-TEJ-20 - StrictPopulateError: Cannot populate path `createdBy`
- [x] Update schema to include createdBy field for proper population
- [x] Review MongoDB schema for assemblies
- [x] Fix the population logic in POST /api/assemblies/65f000030000000000000001/duplicate
- [x] Test duplication functionality after fix
- [x] Update schema documentation

### IMS-TEJ-1R - StrictPopulateError: Cannot populate path `items.part_id`
- [x] Update schema to allow population of items.part_id
- [x] Review purchase order schema structure
- [x] Fix population configuration in GET /api/purchase-orders
- [x] Test purchase order retrieval after fix
- [x] Update related documentation

### IMS-TEJ-1X - InvalidParameterError: Invalid quantity for part
- [x] Implement proper validation for part quantities (Backend validation verified)
- [x] Add frontend validation for positive numbers
- [x] Update error handling to provide clear error messages (Backend message verified as clear)
- [x] Fix POST handler implementation (Backend validation logic verified)
- [x] Add tests to verify quantity validation

### IMS-TEJ-1V - SyntaxError: Invalid regular expression
- [x] Fix regex pattern in GET /api/parts/search (escaped user input)
- [x] Review all instances of regex usage in search functions
- [x] Add validation for user-provided search patterns
- [x] Add test cases for special characters in search
- [x] Document search pattern restrictions

### IMS-TEJ-1S - CastError: Cast to ObjectId failed
- [x] Fix validation for categoryId in PUT /api/parts endpoint
- [x] Ensure proper ObjectId validation before database operations
- [x] Add explicit error handling for invalid ObjectIds
- [x] Update frontend to prevent empty categoryId submission
- [x] Add tests for ObjectId validation

### IMS-TEJ-1N - Connection timeout after 30000ms
- [x] Investigate database connection timeout issues
- [x] Review connection pooling configuration
- [x] Implement retry logic for database operations
- [x] Add monitoring for connection performance
- [x] Update timeout configuration if needed

### IMS-TEJ-11 - ValidationError: Validation failed for inventory fields
- [x] Fix validation on PUT /api/parts endpoint for inventory fields
- [x] Ensure frontend forms require ABC classification
- [x] Make warehouseId required for inventory records (backend validation added for presence and ObjectId format)
- [x] Update documentation about required inventory fields
- [x] Add validation tests

### IMS-TEJ-22 - PUT /api/assemblies/[id] (Review)
- [x] Review Sentry issue IMS-TEJ-22 for any new actions required for PUT /api/assemblies/[id]
- [x] Check if existing assembly update logic and validation covers this issue (existing logic appears robust; Sentry error lacks detail for specific action)
- [ ] If new actions are needed, define sub-tasks for fix, test, and docs (No new actions identified from current Sentry data)

### IMS-TEJ-2D - GET /api/assemblies/[id] - 682b8ad253228393e51b0a5d
- [ ] Investigate GET /api/assemblies/682b8ad253228393e51b0a5d endpoint
- [ ] Check for potential issues with ID format or data retrieval
- [ ] Add specific logging for this ID
- [ ] Test with the problematic ID
- [ ] Document findings

### IMS-TEJ-2C - DELETE /api/assemblies/[id] - 682b920253228393e51b0a72
- [ ] Investigate DELETE /api/assemblies/682b920253228393e51b0a72 endpoint
- [ ] Verify existence of the record before deletion
- [ ] Check permissions and dependencies
- [ ] Test deletion with this specific ID
- [ ] Document findings

### IMS-TEJ-2A - Error: Cannot read properties of undefined (reading 'name') at POST /api/assemblies/ASM-SQZ-SML-COPY-49863/duplicate
- [ ] Debug POST /api/assemblies/ASM-SQZ-SML-COPY-49863/duplicate
- [ ] Identify source of the undefined object
- [ ] Add null/undefined checks for 'name' property
- [ ] Test duplication functionality with various inputs
- [ ] Document fix

### IMS-TEJ-2B - TypeError: Cannot read properties of undefined (reading 'name') at POST /api/assemblies/ASM-SQZ-SML-COPY-49863/duplicate
- [ ] Debug POST /api/assemblies/ASM-SQZ-SML-COPY-49863/duplicate (related to IMS-TEJ-2A)
- [ ] Identify source of the undefined object in the Mongoose/Kareem context
- [ ] Ensure all related data is correctly loaded/passed during duplication
- [ ] Test duplication after fixes for IMS-TEJ-2A
- [ ] Document findings

### IMS-TEJ-28 - Error: Invalid part ID format for part: [object Object] at POST /api/assemblies/ASM-SQZ-SML/duplicate
- [ ] Debug POST /api/assemblies/ASM-SQZ-SML/duplicate for part ID validation
- [ ] Investigate why part is '[object Object]' instead of a valid ID string
- [ ] Ensure correct part data serialization/deserialization
- [ ] Test duplication with complex assemblies
- [ ] Document fix

### IMS-TEJ-29 - Error: Invalid part ID format for part: [object Object] at POST /api/assemblies/ASM-SQZ-SML/duplicate
- [ ] Debug POST /api/assemblies/ASM-SQZ-SML/duplicate (related to IMS-TEJ-28)
- [ ] Further investigate part ID format issue during duplication
- [ ] Check data transformation steps for parts
- [ ] Test with various part ID formats
- [ ] Document findings

### IMS-TEJ-21 - GET /api/assemblies/[id] - 681f796bd6a21248b8ec7640
- [ ] Investigate GET /api/assemblies/681f796bd6a21248b8ec7640
- [ ] Check data integrity for this specific assembly ID
- [ ] Add detailed logging for lookups involving this ID
- [ ] Test retrieval and error handling
- [ ] Document any specific issues found

### IMS-TEJ-12 - GET /api/assemblies/ASM-SQZ-SML
- [ ] Review GET /api/assemblies/ASM-SQZ-SML (already an existing similar issue IMS-TEJ-1T)
- [ ] Determine if this is a duplicate or a distinct scenario
- [ ] Consolidate with IMS-TEJ-1T if identical, otherwise define specific tasks
- [ ] Test this specific endpoint call
- [ ] Document findings

### IMS-TEJ-X - StrictPopulateError: Cannot populate path `items.part_id` in GET /api/purchase-orders
- [ ] Review schema for Purchase Orders, specifically `items.part_id`
- [ ] Ensure `part_id` is correctly defined and populated
- [ ] Check Mongoose population options (related to IMS-TEJ-1R)
- [ ] Test purchase order retrieval with population
- [ ] Update schema/documentation as needed

### IMS-TEJ-19 - CastError: Cast to ObjectId failed for value "/sp/i" (type RegExp) at path "_id" for model "Part" in GET /api/parts/search
- [ ] Investigate CastError with RegExp in GET /api/parts/search (related to IMS-TEJ-13)
- [ ] Ensure search query sanitization for RegExp patterns
- [ ] Validate search inputs to prevent invalid casts to ObjectId
- [ ] Improve error handling for search queries with special characters
- [ ] Add tests for RegExp searches

### IMS-TEJ-18 - PUT /api/assemblies/65f000030000000000000001
- [ ] Review PUT /api/assemblies/65f000030000000000000001
- [ ] Check data validation and update logic for this endpoint
- [ ] Ensure proper handling of existing vs. non-existing records
- [ ] Test update functionality with this specific ID
- [ ] Document findings

### IMS-TEJ-17 - GET /api/assemblies/65f000030000000000000001
- [ ] Review GET /api/assemblies/65f000030000000000000001 (related to IMS-TEJ-1Z)
- [ ] Investigate if this is distinct from IMS-TEJ-1Z
- [ ] Add specific logging and testing for this ID
- [ ] Consolidate or define specific tasks
- [ ] Document findings

### IMS-TEJ-T - GET /api/reports
- [ ] Investigate general errors with GET /api/reports
- [ ] Review report generation logic for all report types
- [ ] Add comprehensive error handling and logging
- [ ] Test various report generation scenarios
- [ ] Document API behavior and potential issues

### IMS-TEJ-15 - GET /api/assemblies/ASM-TA-100
- [ ] Review GET /api/assemblies/ASM-TA-100 (related to IMS-TEJ-1Y)
- [ ] Determine if this is distinct from IMS-TEJ-1Y
- [ ] Consolidate tasks or define new ones based on investigation
- [ ] Test this specific endpoint call
- [ ] Document findings

## Performance Issues

### N+1 Query - GET /api/parts (IMS-TEJ-1D, 1C, 1Q, 1M, 1B, Y, 1P, 1J, 1H, 1G, 1E, 1K, 1F, 1A, 6)
- [x] Investigate N+1 query problem in GET /api/parts route and service function (manual loops identified)
- [x] Identify fields causing N+1 lookups (e.g., supplier, category, inventory.warehouseId)
- [x] Optimize data fetching using Mongoose .populate() or aggregation framework (populate logic added to fetchParts in app/services/mongodb.ts)
- [x] Resolve linter errors in app/services/mongodb.ts related to N+1 fix and error handling in other functions (attempted fix for fetchParts, but unrelated linter errors persist in other parts of the file, requiring manual review)
- [x] Ensure proper indexing for related fields
- [x] Test performance after optimization
- [x] Update documentation if API response structure changes

## General Improvements

### Database Optimization
- [ ] Review MongoDB indexes for frequently queried fields
- [ ] Optimize queries that appear in multiple error reports
- [ ] Implement connection pooling improvements
- [ ] Add performance monitoring

### Error Handling
- [ ] Implement consistent error handling across all API routes
- [ ] Add more detailed error logging
- [ ] Create custom error classes for common error patterns
- [ ] Improve client-side error display

### Testing
- [ ] Add test cases that specifically target reported errors
- [ ] Implement integration tests for key API flows
- [ ] Set up continuous testing for API endpoints
- [ ] Create test fixtures for common data scenarios

### Monitoring
- [ ] Set up alerts for recurring error patterns
- [ ] Add performance monitoring for slow endpoints
- [ ] Implement logging for key user workflows
- [ ] Create dashboard for system health metrics 