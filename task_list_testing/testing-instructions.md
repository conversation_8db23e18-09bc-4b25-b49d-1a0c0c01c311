# UI/UX Testing Instructions

This folder contains several files to help you conduct a comprehensive UI/UX test of the Trend IMS application.

## Files Included

1. **ui-ux-testing-plan.md**: A detailed checklist of what to test
2. **ui-ux-findings.md**: A template to record your observations and findings
3. **capture-console-errors.js**: A script to help log console errors during testing

## Testing Steps

### 1. Preparation

1. Make sure your Trend IMS application is running at `http://localhost:5174/`
2. Open Microsoft Edge or Brave browser
3. Navigate to `http://localhost:5174/`
4. Open browser developer tools (F12 or right-click > Inspect)
5. Go to the Console tab in developer tools

### 2. Set Up Console Error Logging

1. Open the `capture-console-errors.js` file in this folder
2. Copy the entire code
3. Paste it into the browser's console and press Enter
4. You should see a green message confirming the logger is active

### 3. Conduct Testing

1. Open `ui-ux-testing-plan.md` and use it as your guide
2. Go through each section methodically
3. As you test, record your findings in `ui-ux-findings.md`
4. For each section in the findings document:
   - Replace the placeholder text with your observations
   - Add screenshots if helpful
   - Note any issues encountered
   - Rate severity of issues (Critical, High, Medium, Low)

### 4. Capture Console Errors

After testing each major section or the entire application:

1. In the browser console, type `window.exportErrorLog()` and press Enter
2. This will generate a formatted report of all console errors and warnings
3. Copy this report and paste it into the "Console Errors" section of your findings document

### 5. Complete Your Report

1. After testing all sections, fill in the "Summary of Findings" section
2. Prioritize the issues you found by severity
3. Add any recommendations for improvements
4. Save your completed `ui-ux-findings.md` file

## Tips for Effective Testing

- Test one section/feature completely before moving to the next
- Try different input variations for forms (valid, invalid, empty)
- Test with keyboard navigation as well as mouse
- Check how the application behaves at different screen sizes
- Pay attention to loading states and error handling
- Note any inconsistencies in design or behavior
- Consider the overall user experience and workflow efficiency 