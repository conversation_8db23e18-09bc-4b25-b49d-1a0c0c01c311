# Database Connection Implementation Guide

## Overview
This document provides technical implementation details for improving the database connection handling in the Trend_IMS inventory management system, focusing on reliability, performance, and error handling.

## Current Issues
Based on the Sentry issues and task lists reviewed, the following problems have been identified:
- Intermittent MongoDB connection errors
- Lack of proper connection state tracking
- Insufficient error handling in inventory API routes
- Missing timeout handling for long-running queries

## Implementation Details

### 1. Enhanced Connection Manager

```typescript
// app/lib/mongodb.ts
import { MongoClient, Db, MongoClientOptions } from 'mongodb';
import { Mutex } from 'async-mutex';

enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

class MongoDBConnectionManager {
  private static instance: MongoDBConnectionManager;
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private connectionMutex = new Mutex();
  private connectionPromise: Promise<Db> | null = null;
  
  private constructor() {}
  
  public static getInstance(): MongoDBConnectionManager {
    if (!MongoDBConnectionManager.instance) {
      MongoDBConnectionManager.instance = new MongoDBConnectionManager();
    }
    return MongoDBConnectionManager.instance;
  }
  
  public async connect(): Promise<Db> {
    // Use mutex to prevent multiple concurrent connection attempts
    return this.connectionMutex.runExclusive(async () => {
      if (this.connectionState === ConnectionState.CONNECTED && this.db) {
        return this.db;
      }
      
      if (this.connectionPromise) {
        return this.connectionPromise;
      }
      
      this.connectionState = ConnectionState.CONNECTING;
      console.log('Establishing MongoDB connection...');
      
      const uri = process.env.MONGODB_URI;
      if (!uri) {
        this.connectionState = ConnectionState.ERROR;
        throw new Error('MongoDB URI not provided in environment variables');
      }
      
      const options: MongoClientOptions = {
        bufferCommands: true,
        connectTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        maxPoolSize: 50,
        minPoolSize: 5,
        maxIdleTimeMS: 120000,
        waitQueueTimeoutMS: 10000,
      };
      
      try {
        this.connectionPromise = new Promise(async (resolve, reject) => {
          try {
            // Add timeout for connection attempt
            const timeoutId = setTimeout(() => {
              reject(new Error('MongoDB connection timeout after 10 seconds'));
            }, 10000);
            
            this.client = new MongoClient(uri, options);
            await this.client.connect();
            this.db = this.client.db();
            
            clearTimeout(timeoutId);
            
            // Set up connection monitoring
            this.client.on('close', this.handleDisconnect.bind(this));
            this.client.on('error', this.handleError.bind(this));
            
            console.log('MongoDB connection established successfully');
            this.connectionState = ConnectionState.CONNECTED;
            resolve(this.db);
          } catch (error) {
            this.connectionState = ConnectionState.ERROR;
            console.error('MongoDB connection error:', error);
            reject(error);
          }
        });
        
        return await this.connectionPromise;
      } catch (error) {
        this.connectionState = ConnectionState.ERROR;
        this.connectionPromise = null;
        throw error;
      }
    });
  }
  
  public async disconnect(): Promise<void> {
    if (this.client && this.connectionState === ConnectionState.CONNECTED) {
      await this.client.close();
      this.client = null;
      this.db = null;
      this.connectionState = ConnectionState.DISCONNECTED;
      this.connectionPromise = null;
      console.log('MongoDB connection closed');
    }
  }
  
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }
  
  private handleDisconnect(): void {
    console.log('MongoDB connection closed unexpectedly');
    this.connectionState = ConnectionState.DISCONNECTED;
    this.db = null;
    this.connectionPromise = null;
  }
  
  private handleError(error: Error): void {
    console.error('MongoDB connection error:', error);
    this.connectionState = ConnectionState.ERROR;
  }
  
  public async withDatabase<T>(operation: (db: Db) => Promise<T>): Promise<T> {
    try {
      const db = await this.connect();
      return await operation(db);
    } catch (error) {
      console.error('Error executing database operation:', error);
      throw error;
    }
  }
}

export default MongoDBConnectionManager.getInstance();
```

### 2. Inventory-Specific Middleware

```typescript
// app/middlewares/withInventoryDatabase.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { NextResponse } from 'next/server';
import mongoDBManager from '../lib/mongodb';
import { InventoryConnectionError } from '../types/errors';

export function withInventoryDatabase(handler: any) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      // Ensure database connection before proceeding
      await mongoDBManager.connect();
      
      // Add inventory-specific context to the request
      req.inventoryContext = {
        collectionName: 'inventory',
        timestamp: new Date().toISOString()
      };
      
      // Execute the handler
      return await handler(req, res);
    } catch (error) {
      console.error('Inventory database middleware error:', error);
      
      // Create a structured error response
      const errorResponse = {
        status: 'error',
        code: error instanceof InventoryConnectionError ? 'INVENTORY_CONNECTION_ERROR' : 'INVENTORY_DATABASE_ERROR',
        message: error instanceof Error ? error.message : 'Unknown inventory database error',
        timestamp: new Date().toISOString()
      };
      
      // Report to monitoring system if needed
      // reportError(errorResponse);
      
      return res.status(500).json(errorResponse);
    }
  };
}

// For App Router
export function withInventoryDatabaseAppRouter(handler: any) {
  return async (request: Request) => {
    try {
      // Ensure database connection before proceeding
      await mongoDBManager.connect();
      
      // Execute the handler
      return await handler(request);
    } catch (error) {
      console.error('Inventory database middleware error:', error);
      
      // Create a structured error response
      const errorResponse = {
        status: 'error',
        code: error instanceof InventoryConnectionError ? 'INVENTORY_CONNECTION_ERROR' : 'INVENTORY_DATABASE_ERROR',
        message: error instanceof Error ? error.message : 'Unknown inventory database error',
        timestamp: new Date().toISOString()
      };
      
      // Return error response
      return NextResponse.json(errorResponse, { status: 500 });
    }
  };
}
```

### 3. Custom Error Types

```typescript
// app/types/errors.ts
export class InventoryError extends Error {
  code: string;
  timestamp: string;
  context?: Record<string, any>;
  
  constructor(message: string, code: string, context?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.timestamp = new Date().toISOString();
    this.context = context;
  }
}

export class InventoryConnectionError extends InventoryError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'INVENTORY_CONNECTION_ERROR', context);
  }
}

export class InventoryQueryError extends InventoryError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'INVENTORY_QUERY_ERROR', context);
  }
}

export class InventoryValidationError extends InventoryError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'INVENTORY_VALIDATION_ERROR', context);
  }
}

export class InventoryNotFoundError extends InventoryError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'INVENTORY_NOT_FOUND', context);
  }
}
```

### 4. Connection Monitoring Service

```typescript
// app/services/connectionMonitoring.ts
import mongoDBManager from '../lib/mongodb';

class ConnectionMonitoringService {
  private static instance: ConnectionMonitoringService;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private healthCallbacks: Array<(status: boolean) => void> = [];
  
  private constructor() {}
  
  public static getInstance(): ConnectionMonitoringService {
    if (!ConnectionMonitoringService.instance) {
      ConnectionMonitoringService.instance = new ConnectionMonitoringService();
    }
    return ConnectionMonitoringService.instance;
  }
  
  public startMonitoring(intervalMs: number = 30000): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.checkConnection();
      } catch (error) {
        console.error('Connection monitoring error:', error);
      }
    }, intervalMs);
    
    console.log(`Database connection monitoring started with ${intervalMs}ms interval`);
  }
  
  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('Database connection monitoring stopped');
    }
  }
  
  public onHealthChange(callback: (status: boolean) => void): () => void {
    this.healthCallbacks.push(callback);
    
    // Return function to remove the callback
    return () => {
      this.healthCallbacks = this.healthCallbacks.filter(cb => cb !== callback);
    };
  }
  
  private async checkConnection(): Promise<boolean> {
    try {
      const db = await mongoDBManager.connect();
      // Perform a lightweight operation to verify connection
      await db.command({ ping: 1 });
      
      const isHealthy = true;
      this.notifyHealthCallbacks(isHealthy);
      return isHealthy;
    } catch (error) {
      console.error('Database connection health check failed:', error);
      
      const isHealthy = false;
      this.notifyHealthCallbacks(isHealthy);
      return isHealthy;
    }
  }
  
  private notifyHealthCallbacks(status: boolean): void {
    for (const callback of this.healthCallbacks) {
      try {
        callback(status);
      } catch (error) {
        console.error('Error in health callback:', error);
      }
    }
  }
}

export default ConnectionMonitoringService.getInstance();
```

### 5. Implementation in API Routes

```typescript
// app/api/inventory/route.ts
import { NextResponse } from 'next/server';
import { withInventoryDatabaseAppRouter } from '../../middlewares/withInventoryDatabase';
import mongoDBManager from '../../lib/mongodb';
import { InventoryValidationError, InventoryQueryError } from '../../types/errors';

async function getInventoryItems(request: Request) {
  const { searchParams } = new URL(request.url);
  const category = searchParams.get('category');
  const limit = parseInt(searchParams.get('limit') || '50', 10);
  const page = parseInt(searchParams.get('page') || '1', 10);
  const skip = (page - 1) * limit;
  
  try {
    // Validate parameters
    if (limit > 100) {
      throw new InventoryValidationError('Limit cannot exceed 100 items per page', { limit });
    }
    
    // Use the connection manager to get database instance
    return await mongoDBManager.withDatabase(async (db) => {
      const collection = db.collection('inventory');
      
      // Build query based on parameters
      const query = category ? { category } : {};
      
      // Set timeout for the operation
      const options = { maxTimeMS: 5000 };
      
      // Execute query with pagination
      const [items, total] = await Promise.all([
        collection.find(query, options).skip(skip).limit(limit).toArray(),
        collection.countDocuments(query)
      ]);
      
      return NextResponse.json({
        status: 'success',
        data: {
          items,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
          }
        }
      });
    });
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    
    if (error instanceof InventoryValidationError) {
      return NextResponse.json({
        status: 'error',
        code: error.code,
        message: error.message
      }, { status: 400 });
    }
    
    return NextResponse.json({
      status: 'error',
      code: error instanceof InventoryQueryError ? error.code : 'INVENTORY_ERROR',
      message: error instanceof Error ? error.message : 'Unknown error fetching inventory items'
    }, { status: 500 });
  }
}

export const GET = withInventoryDatabaseAppRouter(getInventoryItems);
```

## Implementation Steps

1. Create the enhanced MongoDB connection manager
2. Implement custom error types for inventory operations
3. Create the inventory-specific database middleware
4. Set up the connection monitoring service
5. Update all inventory API routes to use the new middleware
6. Add comprehensive error handling to all inventory endpoints
7. Implement connection status indicators in the UI
8. Set up Sentry error reporting with inventory-specific context

## Testing Strategy

1. Unit tests for the connection manager and error handling
2. Integration tests for database middleware
3. Stress tests to verify connection pooling and retry logic
4. Simulated failure tests to verify error handling
5. End-to-end tests for inventory API endpoints

## Monitoring and Metrics

1. Connection state transitions
2. Connection attempt success/failure rates
3. Query execution times
4. Error rates by type
5. Connection pool utilization

## Rollout Plan

1. Implement changes in development environment
2. Conduct thorough testing with simulated failures
3. Deploy to staging environment for integration testing
4. Monitor performance and error rates
5. Gradually roll out to production with feature flags
6. Monitor closely for the first 48 hours after deployment