# MongoDB Schema Refactoring Checklist

This document outlines the necessary steps to refactor the IMS database schema based on the differences between the old and updated MongoDB schemas. It provides a comprehensive plan for migration, code updates, testing, and rollback strategies.

## 1. Schema Changes Analysis

### 1.1 Primary Key and Reference Changes

| Collection | Field | Old Schema | New Schema | Impact |
|------------|-------|------------|------------|--------|
| `parts` | `_id` | String (part number) | ObjectId | Major - affects all references to parts |
| `parts` | `partNumber` | Not separate (was _id) | String (indexed) | New field to store business identifier |
| `parts` | `inventory.location` | String | Removed | Replaced with warehouseId reference |
| `parts` | `inventory.warehouseId` | Not present | ObjectId | New reference to warehouses._id |
| `parts` | `supplierId` | Not present or String | ObjectId | Reference to suppliers._id |
| `parts` | `categoryId` | Not present or String | ObjectId | Reference to categories._id |
| `transactions` | `partId` | String | ObjectId | Reference to parts._id |
| `assemblies` | `partsRequired.partId` | String | ObjectId | Reference to parts._id |
| `workorders` | `partIdToManufacture` | String | ObjectId | Reference to parts._id |

### 1.2 New Fields and Expanded Objects

| Collection | Field/Object | Change | Impact |
|------------|-------------|--------|--------|
| `parts` | `inventory` | Expanded with safetyStockLevel, maximumStockLevel, averageDailyUsage, abcClassification | Enhanced inventory management |
| `suppliers` | `payment_terms`, `delivery_terms`, `is_active` | New fields | Additional supplier management data |
| `users` | `lastLoginAt` | New field | User activity tracking |
| `batches` | `quantityScrapped` | New field | Production quality tracking |
| `deliveries` | `itemsDelivered` | Expanded structure | More detailed delivery tracking |
| All collections | `updatedAt` | Added consistently | Better change tracking |

### 1.3 Validation and Constraints

- More explicit indexing requirements (e.g., `parts.partNumber` should be indexed)
- More detailed relationship definitions between collections
- Enhanced schema validators needed for all collections

## 2. Migration Scripts

### 2.1 Parts Collection Migration

```javascript
// 1. Create a temporary collection to store the mapping between old part numbers and new ObjectIds
db.createCollection("partIdMapping");

// 2. Generate new ObjectIds for each part and store the mapping
db.parts.find().forEach(function(part) {
  const oldId = part._id;
  const newId = ObjectId();
  db.partIdMapping.insertOne({
    oldId: oldId,
    newId: newId,
    partNumber: oldId // The old _id becomes the partNumber
  });
});

// 3. Create new parts collection with updated schema
db.createCollection("parts_new");

// 4. Migrate data with new structure
db.parts.find().forEach(function(part) {
  const mapping = db.partIdMapping.findOne({ oldId: part._id });
  
  // Create new part document with updated schema
  const newPart = {
    _id: mapping.newId,
    partNumber: mapping.partNumber,
    name: part.name,
    description: part.description || "",
    technical_specs: part.technical_specs || null,
    is_manufactured: part.is_manufactured || false,
    reorder_level: part.reorder_level || null,
    status: part.status || "active",
    inventory: {
      currentStock: part.inventory?.currentStock || 0,
      warehouseId: null, // Will update in a separate step
      safetyStockLevel: 0, // Default value
      maximumStockLevel: 100, // Default value
      averageDailyUsage: 0.0, // Default value
      abcClassification: "C", // Default value
      lastStockUpdate: part.inventory?.lastCountDate || new Date()
    },
    supplierId: null, // Will update in a separate step
    unitOfMeasure: "pcs", // Default value
    costPrice: 0.0, // Default value
    categoryId: null, // Will update in a separate step
    createdAt: part.createdAt || new Date(),
    updatedAt: new Date()
  };
  
  db.parts_new.insertOne(newPart);
});

// 5. Update warehouse references
db.parts.find().forEach(function(part) {
  const mapping = db.partIdMapping.findOne({ oldId: part._id });
  if (part.inventory && part.inventory.location) {
    const warehouse = db.warehouses.findOne({ name: part.inventory.location });
    if (warehouse) {
      db.parts_new.updateOne(
        { _id: mapping.newId },
        { $set: { "inventory.warehouseId": warehouse._id } }
      );
    }
  }
});

// 6. Rename collections to complete migration
db.parts.renameCollection("parts_old");
db.parts_new.renameCollection("parts");

// 7. Create necessary indexes
db.parts.createIndex({ "partNumber": 1 }, { unique: true });
db.parts.createIndex({ "name": 1 });
db.parts.createIndex({ "categoryId": 1 });
db.parts.createIndex({ "supplierId": 1 });
```

### 2.2 Transactions Collection Migration

```javascript
// 1. Create new transactions collection with updated schema
db.createCollection("transactions_new");

// 2. Migrate data with updated references
db.transactions.find().forEach(function(transaction) {
  const partMapping = db.partIdMapping.findOne({ oldId: transaction.partId });
  const warehouse = db.warehouses.findOne({ name: transaction.warehouseName || "Unknown" });
  
  const newTransaction = {
    _id: transaction._id,
    partId: partMapping ? partMapping.newId : null,
    warehouseId: warehouse ? warehouse._id : null,
    transactionType: transaction.transactionType,
    quantity: transaction.quantity,
    previousStock: transaction.previousStock,
    newStock: transaction.newStock,
    transactionDate: transaction.transactionDate,
    referenceNumber: transaction.referenceNumber || null,
    referenceType: transaction.referenceType || null,
    userId: transaction.userId || null, // May need to map user references
    notes: transaction.notes || "",
    createdAt: transaction.createdAt || new Date()
  };
  
  db.transactions_new.insertOne(newTransaction);
});

// 3. Rename collections to complete migration
db.transactions.renameCollection("transactions_old");
db.transactions_new.renameCollection("transactions");

// 4. Create necessary indexes
db.transactions.createIndex({ "partId": 1 });
db.transactions.createIndex({ "warehouseId": 1 });
db.transactions.createIndex({ "transactionDate": -1 });
db.transactions.createIndex({ "referenceNumber": 1 });
```

### 2.3 Assemblies Collection Migration

```javascript
// 1. Create new assemblies collection with updated schema
db.createCollection("assemblies_new");

// 2. Migrate data with updated references
db.assemblies.find().forEach(function(assembly) {
  const newPartsRequired = [];
  
  // Map each part reference to new ObjectId
  if (assembly.partsRequired && Array.isArray(assembly.partsRequired)) {
    assembly.partsRequired.forEach(function(partReq) {
      const partMapping = db.partIdMapping.findOne({ oldId: partReq.partId });
      if (partMapping) {
        newPartsRequired.push({
          partId: partMapping.newId,
          quantityRequired: partReq.quantityRequired || 1,
          unitOfMeasure: partReq.unitOfMeasure || "pcs"
        });
      }
    });
  }
  
  const newAssembly = {
    _id: assembly._id,
    assemblyCode: assembly.assemblyCode,
    name: assembly.name,
    productId: assembly.productId,
    parentId: assembly.parentId,
    isTopLevel: assembly.isTopLevel || false,
    partsRequired: newPartsRequired,
    status: assembly.status || "active",
    version: assembly.version || 1,
    manufacturingInstructions: assembly.manufacturingInstructions || null,
    estimatedBuildTime: assembly.estimatedBuildTime || null,
    createdAt: assembly.createdAt || new Date(),
    updatedAt: new Date()
  };
  
  db.assemblies_new.insertOne(newAssembly);
});

// 3. Rename collections to complete migration
db.assemblies.renameCollection("assemblies_old");
db.assemblies_new.renameCollection("assemblies");

// 4. Create necessary indexes
db.assemblies.createIndex({ "assemblyCode": 1 }, { unique: true });
db.assemblies.createIndex({ "productId": 1 });
db.assemblies.createIndex({ "parentId": 1 });
```

### 2.4 Work Orders Collection Migration

```javascript
// 1. Create new workorders collection with updated schema
db.createCollection("workorders_new");

// 2. Migrate data with updated references
db.workorders.find().forEach(function(workorder) {
  const partMapping = workorder.partIdToManufacture ? 
    db.partIdMapping.findOne({ oldId: workorder.partIdToManufacture }) : null;
  
  const newWorkorder = {
    _id: workorder._id,
    woNumber: workorder.woNumber,
    assemblyId: workorder.assemblyId,
    partIdToManufacture: partMapping ? partMapping.newId : null,
    productId: workorder.productId,
    quantity: workorder.quantity,
    status: workorder.status,
    priority: workorder.priority,
    dueDate: workorder.dueDate || new Date(),
    startDate: workorder.startDate || null,
    completedAt: workorder.completedAt || null,
    assignedTo: workorder.assignedTo,
    notes: workorder.notes || "",
    sourceDemand: workorder.sourceDemand || null,
    createdAt: workorder.createdAt || new Date(),
    updatedAt: new Date()
  };
  
  db.workorders_new.insertOne(newWorkorder);
});

// 3. Rename collections to complete migration
db.workorders.renameCollection("workorders_old");
db.workorders_new.renameCollection("workorders");

// 4. Create necessary indexes
db.workorders.createIndex({ "woNumber": 1 }, { unique: true });
db.workorders.createIndex({ "assemblyId": 1 });
db.workorders.createIndex({ "partIdToManufacture": 1 });
db.workorders.createIndex({ "status": 1 });
db.workorders.createIndex({ "dueDate": 1 });
```

### 2.5 Schema Validators Update

```javascript
// Example for parts collection validator
db.runCommand({
  collMod: "parts",
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["partNumber", "name", "inventory", "status", "createdAt", "updatedAt"],
      properties: {
        partNumber: {
          bsonType: "string",
          description: "Unique business identifier for the part"
        },
        name: {
          bsonType: "string",
          description: "Name of the part"
        },
        // Add other field validations as needed
      }
    }
  },
  validationLevel: "moderate"
});

// Repeat for other collections
```

## 3. Models and Services Updates

### 3.1 Models to Update

| Model File | Changes Required |
|------------|------------------|
| `app/models/part.model.ts` | - Change _id type from String to ObjectId<br>- Add partNumber field<br>- Update inventory object structure<br>- Add warehouseId, supplierId, categoryId as ObjectId references<br>- Add updatedAt field |
| `app/models/transaction.model.ts` | - Change partId from String to ObjectId<br>- Add warehouseId as ObjectId reference<br>- Add userId as ObjectId reference |
| `app/models/assembly.model.ts` | - Update partsRequired.partId from String to ObjectId |
| `app/models/workorder.model.ts` | - Change partIdToManufacture from String to ObjectId |
| `app/models/supplier.model.ts` | - Add payment_terms, delivery_terms, is_active fields |
| `app/models/user.model.ts` | - Add lastLoginAt field |
| `app/models/batch.model.ts` | - Add quantityScrapped field |
| `app/models/delivery.model.ts` | - Update itemsDelivered structure |

### 3.2 Services to Update

| Service File | Changes Required |
|--------------|------------------|
| `app/services/part.service.ts` | - Update CRUD operations to handle ObjectId for _id<br>- Add logic for partNumber field<br>- Update inventory management functions |
| `app/services/inventory.service.ts` | - Update all references to parts from String to ObjectId<br>- Modify warehouse reference handling |
| `app/services/assembly.service.ts` | - Update part reference handling in BOM functions |
| `app/services/workorder.service.ts` | - Update part reference handling |
| `app/services/report.service.ts` | - Update all queries and aggregations using part references |

### 3.3 API Endpoints to Update

| API File | Changes Required |
|----------|------------------|
| `app/api/parts/[id]/route.ts` | - Update to handle ObjectId for part identification |
| `app/api/inventory/transactions/route.ts` | - Update to handle ObjectId for part references |
| `app/api/assemblies/route.ts` | - Update part reference handling in request/response |
| `app/api/workorders/route.ts` | - Update part reference handling in request/response |

## 4. Testing Strategy

### 4.1 Unit Tests

- Update model validation tests for new schema structure
- Test ObjectId reference handling in all models
- Verify data transformation functions in services

### 4.2 Integration Tests

- Test API endpoints with new ObjectId references
- Verify correct relationships between collections
- Test inventory transactions with new warehouse references

### 4.3 Migration Tests

- Create test dataset with old schema
- Run migration scripts on test data
- Verify data integrity after migration
- Test rollback procedures

### 4.4 Data Integrity Verification

```javascript
// Script to verify data integrity after migration
function verifyDataIntegrity() {
  // Check all parts have valid partNumber
  const partsWithoutPartNumber = db.parts.countDocuments({ partNumber: { $exists: false } });
  print(`Parts without partNumber: ${partsWithoutPartNumber}`);
  
  // Check all transactions have valid part references
  const transactions = db.transactions.find().toArray();
  let invalidPartRefs = 0;
  
  transactions.forEach(function(transaction) {
    const partExists = db.parts.countDocuments({ _id: transaction.partId });
    if (partExists === 0) invalidPartRefs++;
  });
  
  print(`Transactions with invalid part references: ${invalidPartRefs}`);
  
  // Check all assemblies have valid part references
  const assemblies = db.assemblies.find().toArray();
  let invalidAssemblyPartRefs = 0;
  
  assemblies.forEach(function(assembly) {
    if (assembly.partsRequired && Array.isArray(assembly.partsRequired)) {
      assembly.partsRequired.forEach(function(partReq) {
        const partExists = db.parts.countDocuments({ _id: partReq.partId });
        if (partExists === 0) invalidAssemblyPartRefs++;
      });
    }
  });
  
  print(`Assembly parts with invalid references: ${invalidAssemblyPartRefs}`);
  
  // More integrity checks as needed
}
```

## 5. Rollback Strategy

### 5.1 Shadow Collections Approach

1. Keep original collections with `_old` suffix for a defined period (e.g., 2 weeks)
2. Implement dual-write capability for critical operations during transition
3. Create rollback scripts that can restore from old collections if needed

### 5.2 Two-Phase Deployment

#### Phase 1: Schema Migration

1. Deploy database changes only
2. Run migration scripts
3. Verify data integrity
4. Monitor system for 24-48 hours

#### Phase 2: Application Code Updates

1. Deploy application code changes to work with new schema
2. Monitor application performance and error rates
3. Have rollback code ready if issues are detected

### 5.3 Rollback Scripts

```javascript
// Example rollback script if needed
function rollbackMigration() {
  // Restore original collections
  if (db.parts_old && db.parts) {
    db.parts.drop();
    db.parts_old.renameCollection("parts");
  }
  
  if (db.transactions_old && db.transactions) {
    db.transactions.drop();
    db.transactions_old.renameCollection("transactions");
  }
  
  if (db.assemblies_old && db.assemblies) {
    db.assemblies.drop();
    db.assemblies_old.renameCollection("assemblies");
  }
  
  if (db.workorders_old && db.workorders) {
    db.workorders.drop();
    db.workorders_old.renameCollection("workorders");
  }
  
  // Drop mapping collection
  if (db.partIdMapping) {
    db.partIdMapping.drop();
  }
  
  print("Rollback completed. Original collections restored.");
}
```

## 6. Implementation Timeline

1. **Preparation Phase (1 week)**
   - Finalize schema design
   - Create test datasets
   - Develop migration scripts

2. **Testing Phase (1 week)**
   - Test migration scripts in staging environment
   - Verify data integrity
   - Update and test application code

3. **Deployment Phase (2 days)**
   - Schedule maintenance window
   - Execute Phase 1: Schema Migration
   - Monitor and verify
   - Execute Phase 2: Application Code Updates

4. **Monitoring Phase (1 week)**
   - Monitor application performance
   - Address any issues
   - Keep rollback capability ready

5. **Cleanup Phase (After 2 weeks)**
   - Remove old collections
   - Remove temporary migration code
   - Document final schema

## 7. Open Questions and Assumptions

1. **Data Volume**: The migration scripts assume a manageable data volume. For very large collections, batch processing may be required.

2. **Downtime Requirements**: The two-phase approach assumes some downtime is acceptable. For zero-downtime requirements, a more complex migration strategy would be needed.

3. **Custom Business Logic**: The migration scripts may need to be adjusted for any custom business logic or data validation rules specific to the IMS implementation.

4. **Indexes**: The checklist assumes the current indexes. Additional indexes may be required based on query patterns.

5. **External Systems**: Any external systems that directly access the MongoDB database will need to be updated to work with the new schema.




# MongoDB Schema Refactoring Checklist

This checklist outlines the necessary steps and code changes to align the current IMS codebase with the updated MongoDB schema. It covers models, services, API endpoints, database migration, testing, and documentation.

## I. Server-Side Changes

### A. Models (`app/models/`)

For each model, ensure the following:
- Fields match the new schema (additions, removals, renames, type changes).
- Validation rules are updated (e.g., `required`, `unique`, `enum`, `minlength`, `maxlength`, custom validators).
- Indexes are updated (e.g., new unique indexes, compound indexes).
- Timestamps (`createdAt`, `updatedAt`) are consistently handled.

1.  **Part Model (`part.model.ts`)**
    -   [ ] Field `partNumber`: Ensure it's unique if specified in the new schema.
    -   [ ] Field `name`: Type `String`, `required`.
    -   [ ] Field `description`: Type `String`.
    -   [ ] Field `category`: Type `ObjectId`, `ref: 'Category'`, `required`.
    -   [ ] Field `material`: Type `String`.
    -   [ ] Field `dimensions`: Object with `length`, `width`, `height`, `unit` (all `Number` or `String` as per schema).
    -   [ ] Field `weight`: Object with `value` (`Number`), `unit` (`String`).
    -   [ ] Field `supplierId`: Type `ObjectId`, `ref: 'Supplier'`, `required`.
    -   [ ] Field `costPrice`: Type `Decimal128`, `required`.
    -   [ ] Field `sellingPrice`: Type `Decimal128`.
    -   [ ] Field `stockDetails`: Object with `quantityOnHand` (`Int32`), `reorderLevel` (`Int32`), `safetyStockLevel` (`Int32`), `lastStockUpdate` (`Date`).
    -   [ ] Field `status`: Type `String`, enum (`active`, `obsolete`, `pending_review`).
    -   [ ] Field `technical_specs`: Array of Objects (`specName`: `String`, `specValue`: `String`).
    -   [ ] Field `is_manufactured`: Type `Boolean`.
    -   [ ] Remove any deprecated fields.
    -   [ ] Update validation rules for all changed/added fields.

2.  **Warehouse Model (`warehouse.model.ts`)**
    -   [ ] Field `warehouseId`: Type `String`, `unique`, `required`.
    -   [ ] Field `name`: Type `String`, `required`.
    -   [ ] Field `location`: Object with `address` (`String`), `city` (`String`), `state` (`String`), `postalCode` (`String`), `country` (`String`), `coordinates` (GeoJSON Point: `type: 'Point'`, `coordinates: [Number, Number]`).
    -   [ ] Field `capacity`: Object with `maxVolume` (`Number`), `currentVolume` (`Number`), `unit` (`String`).
    -   [ ] Field `managerId`: Type `ObjectId`, `ref: 'User'` (assuming a User collection).
    -   [ ] Field `contactInfo`: Object with `phone` (`String`), `email` (`String`).
    -   [ ] Field `operatingHours`: Type `String`.
    -   [ ] Field `status`: Type `String`, enum (`active`, `inactive`, `maintenance`).
    -   [ ] Update validation rules.

3.  **Supplier Model (`supplier.model.ts`)**
    -   [ ] Field `supplierId`: Type `String`, `unique`, `required`.
    -   [ ] Field `name`: Type `String`, `required`.
    -   [ ] Field `contactPerson`: Type `String`.
    -   [ ] Field `contactInfo`: Object with `email` (`String`, `unique`), `phone` (`String`), `address` (`String`).
    -   [ ] Field `paymentTerms`: Type `String`.
    -   [ ] Field `preferredCurrency`: Type `String`.
    -   [ ] Field `rating`: Type `Number` (e.g., 1-5).
    -   [ ] Field `status`: Type `String`, enum (`active`, `inactive`, `preferred`).
    -   [ ] Update validation rules.

4.  **Purchase Order Model (`purchaseorder.model.ts`)**
    -   [ ] Field `poNumber`: Type `String`, `unique`, `required`.
    -   [ ] Field `supplierId`: Type `ObjectId`, `ref: 'Supplier'`, `required`.
    -   [ ] Field `orderDate`: Type `Date`, `required`.
    -   [ ] Field `expectedDeliveryDate`: Type `Date`.
    -   [ ] Field `items`: Array of Objects (`partId`: `ObjectId`, `ref: 'Part'`, `quantity`: `Int32`, `unitPrice`: `Decimal128`).
    -   [ ] Field `totalAmount`: Type `Decimal128`.
    -   [ ] Field `status`: Type `String`, enum (`pending`, `approved`, `ordered`, `partially_received`, `received`, `cancelled`).
    -   [ ] Field `shippingAddress`: Type `String`.
    -   [ ] Field `billingAddress`: Type `String`.
    -   [ ] Field `notes`: Type `String`.
    -   [ ] Field `approvedBy`: Type `ObjectId`, `ref: 'User'`.
    -   [ ] Update validation rules.

5.  **Product Model (`product.model.ts`)**
    -   [ ] Field `productCode`: Type `String`, `unique`, `required`.
    -   [ ] Field `name`: Type `String`, `required`.
    -   [ ] Field `description`: Type `String`.
    -   [ ] Field `categoryId`: Type `ObjectId`, `ref: 'Category'`, `required`.
    -   [ ] Field `billOfMaterials`: Array of Objects (`partId`: `ObjectId`, `ref: 'Part'`, `quantity`: `Int32`).
    -   [ ] Field `sellingPrice`: Type `Decimal128`, `required`.
    -   [ ] Field `status`: Type `String`, enum (`active`, `discontinued`, `in_development`).
    -   [ ] Update validation rules.

6.  **Category Model (`category.model.ts`)**
    -   [ ] Field `categoryId`: Type `String`, `unique`, `required`.
    -   [ ] Field `name`: Type `String`, `required`.
    -   [ ] Field `description`: Type `String`.
    -   [ ] Field `parentCategoryId`: Type `ObjectId`, `ref: 'Category'` (for subcategories, nullable).
    -   [ ] Update validation rules.

7.  **Inventory Transaction Model (`inventorytransaction.model.ts`)**
    -   [ ] Field `transactionId`: Type `String`, `unique`, `required`.
    -   [ ] Field `partId`: Type `ObjectId`, `ref: 'Part'`, `required`.
    -   [ ] Field `warehouseId`: Type `ObjectId`, `ref: 'Warehouse'`, `required`.
    -   [ ] Field `transactionType`: Type `String`, enum (`receipt`, `issue`, `adjustment_in`, `adjustment_out`, `transfer_in`, `transfer_out`).
    -   [ ] Field `quantity`: Type `Int32`, `required`.
    -   [ ] Field `unitPrice`: Type `Decimal128` (cost at time of transaction).
    -   [ ] Field `transactionDate`: Type `Date`, `default: Date.now`.
    -   [ ] Field `referenceId`: Type `ObjectId` (e.g., PO, WO, Delivery ID).
    -   [ ] Field `referenceType`: Type `String` (e.g., `PurchaseOrder`, `WorkOrder`, `Delivery`).
    -   [ ] Field `notes`: Type `String`.
    -   [ ] Field `userId`: Type `ObjectId`, `ref: 'User'` (user performing transaction).
    -   [ ] Field `locationInWarehouse`: Type `String` (e.g., bin number).
    -   [ ] Field `batchCode`: Type `String`.
    -   [ ] Update validation rules.

8.  **Work Order Model (`workorder.model.ts`)**
    -   [ ] Field `woNumber`: Type `String`, `unique`, `required`.
    -   [ ] Field `productId`: Type `ObjectId`, `ref: 'Product'`, `required` (product to be manufactured).
    -   [ ] Field `quantityToProduce`: Type `Int32`, `required`.
    -   [ ] Field `status`: Type `String`, enum (`pending`, `in_progress`, `completed`, `on_hold`, `cancelled`).
    -   [ ] Field `priority`: Type `String`, enum (`low`, `medium`, `high`).
    -   [ ] Field `assignedTo`: Type `ObjectId`, `ref: 'User'`.
    -   [ ] Field `startDate`: Type `Date`.
    -   [ ] Field `expectedCompletionDate`: Type `Date`.
    -   [ ] Field `actualCompletionDate`: Type `Date`.
    -   [ ] Field `notes`: Type `String`.
    -   [ ] Update validation rules.

9.  **Delivery Model (`delivery.model.ts`)**
    -   [ ] Field `deliveryId`: Type `String`, `unique`, `required`.
    -   [ ] Field `referenceType`: Type `String` (e.g., `PurchaseOrder`, `SalesOrder`).
    -   [ ] Field `referenceId`: Type `ObjectId`.
    -   [ ] Field `supplierId`: Type `ObjectId`, `ref: 'Supplier'` (nullable if it's an outgoing customer delivery).
    -   [ ] Field `customerId`: Type `ObjectId`, `ref: 'Customer'` (nullable if it's an incoming supplier delivery).
    -   [ ] Field `status`: Type `String`, enum (`scheduled`, `in_transit`, `delivered`, `delayed`, `cancelled`).
    -   [ ] Field `scheduledDate`: Type `Date`.
    -   [ ] Field `actualDate`: Type `Date`.
    -   [ ] Field `trackingNumber`: Type `String`.
    -   [ ] Field `carrier`: Type `String`.
    -   [ ] Field `notes`: Type `String`.
    -   [ ] Field `receivedBy`: Type `ObjectId`, `ref: 'User'` (for incoming).
    -   [ ] Field `itemsDelivered`: Array of Objects (`partId`: `ObjectId`, `ref: 'Part'`, `productId`: `ObjectId`, `ref: 'Product'`, `quantity`: `Int32`).
    -   [ ] Update validation rules.

10. **Quality Check Model (`qualitycheck.model.ts`)**
    -   [ ] Field `qcId`: Type `String`, `unique`, `required`.
    -   [ ] Field `referenceType`: Type `String` (e.g., `PartReceipt`, `WorkOrderOutput`, `Product`).
    -   [ ] Field `referenceId`: Type `ObjectId`.
    -   [ ] Field `checkDate`: Type `Date`, `default: Date.now`.
    -   [ ] Field `inspectorId`: Type `ObjectId`, `ref: 'User'`, `required`.
    -   [ ] Field `status`: Type `String`, enum (`pass`, `fail`, `pending_rework`).
    -   [ ] Field `notes`: Type `String`.
    -   [ ] Field `defectsFound`: Array of Objects (`defectCode`: `String`, `description`: `String`, `severity`: `String`, `correctiveAction`: `String`).
    -   [ ] Field `attachments`: Array of `String` (URLs or paths to attached files).
    -   [ ] Update validation rules.

11. **User Model (`user.model.ts`)** (Implied, ensure it exists or create)
    -   [ ] Field `userId`: Type `String`, `unique`, `required`.
    -   [ ] Field `username`: Type `String`, `unique`, `required`.
    -   [ ] Field `passwordHash`: Type `String`, `required`.
    -   [ ] Field `email`: Type `String`, `unique`, `required`.
    -   [ ] Field `firstName`: Type `String`.
    -   [ ] Field `lastName`: Type `String`.
    -   [ ] Field `role`: Type `String`, enum (`admin`, `manager`, `staff`, `inspector`).
    -   [ ] Field `status`: Type `String`, enum (`active`, `inactive`, `locked`).
    -   [ ] Update validation rules.

12. **Customer Model (`customer.model.ts`)** (Implied, ensure it exists or create)
    -   [ ] Field `customerId`: Type `String`, `unique`, `required`.
    -   [ ] Field `name`: Type `String`, `required`.
    -   [ ] Field `contactPerson`: Type `String`.
    -   [ ] Field `contactInfo`: Object with `email` (`String`, `unique`), `phone` (`String`).
    -   [ ] Field `shippingAddress`: Object with `street`, `city`, `state`, `postalCode`, `country`.
    -   [ ] Field `billingAddress`: Object with `street`, `city`, `state`, `postalCode`, `country`.
    -   [ ] Field `status`: Type `String`, enum (`active`, `inactive`).
    -   [ ] Update validation rules.

### B. Services (`app/services/`)

For each service corresponding to a model or business logic area:
-   [ ] Update data access logic to use new field names and types.
-   [ ] Adapt business logic to handle new/changed fields and relationships.
-   [ ] Modify Data Transfer Objects (DTOs) if used.
-   [ ] Ensure error handling is robust for schema changes.

1.  **Part Service (`part.service.ts`)**
2.  **Warehouse Service (`warehouse.service.ts`)**
3.  **Supplier Service (`supplier.service.ts`)**
4.  **Purchase Order Service (`purchaseorder.service.ts`)**
5.  **Product Service (`product.service.ts`)**
6.  **Inventory Service (`inventory.service.ts`)** (handles transactions, stock updates)
7.  **Work Order Service (`workorder.service.ts`)**
8.  **Delivery Service (`delivery.service.ts`)**
9.  **Quality Check Service (`qualitycheck.service.ts`)**
10. **User Service / Auth Service (`user.service.ts`, `auth.service.ts`)**
11. **Customer Service (`customer.service.ts`)**

### C. API Endpoints (`app/controllers/` or `app/routes/`)

For each API endpoint:
-   [ ] Update request payloads (validation, structure).
-   [ ] Update response payloads (structure, field names).
-   [ ] Adjust controller logic to interact with updated services.
-   [ ] Ensure API versioning strategy is considered if breaking changes are significant.

1.  **Part Endpoints (`part.controller.ts`)**
2.  **Warehouse Endpoints (`warehouse.controller.ts`)**
3.  **Supplier Endpoints (`supplier.controller.ts`)**
4.  **Purchase Order Endpoints (`purchaseorder.controller.ts`)**
5.  **Product Endpoints (`product.controller.ts`)**
6.  **Inventory Endpoints (`inventory.controller.ts`)**
7.  **Work Order Endpoints (`workorder.controller.ts`)**
8.  **Delivery Endpoints (`delivery.controller.ts`)**
9.  **Quality Check Endpoints (`qualitycheck.controller.ts`)**
10. **User/Auth Endpoints (`user.controller.ts`, `auth.controller.ts`)**
11. **Customer Endpoints (`customer.controller.ts`)**

## II. Client-Side Changes (If Applicable)

-   [ ] **Data Fetching Logic:** Update API calls to match new endpoint structures and payloads.
-   [ ] **UI Components:** Adapt components to display new/changed fields, handle new data structures.
-   [ ] **Forms & Input Validation:** Modify forms for new fields, update client-side validation.
-   [ ] **State Management:** Update client-side state (e.g., Redux, Zustand, Context API) to reflect schema changes.

## III. Database Migration

### A. Migration Scripts (`migrations/`)

For each collection requiring data transformation:
-   [ ] **Field Renaming:** Script using `db.collection.updateMany({}, { $rename: { "oldField": "newField" } })`.
-   [ ] **Field Type Conversion:** Script for converting types (e.g., String to Decimal128, Number to Int32). This might involve custom logic if direct conversion isn't possible.
    -   Example: `db.collection.find({ field: { $type: "string" } }).forEach(doc => { db.collection.updateOne({ _id: doc._id }, { $set: { field: parseFloat(doc.field) } }) });` (adapt for specific types).
-   [ ] **Adding New Fields with Default Values:** Script using `db.collection.updateMany({ newField: { $exists: false } }, { $set: { newField: "defaultValue" } })`.
-   [ ] **Transforming Existing Data to New Structures:** (e.g., for `part.stockDetails`, `warehouse.location`, `delivery.itemsDelivered`). This will likely require more complex update scripts with `$set` and potentially aggregation pipeline stages if data needs to be derived.
-   [ ] **Backfilling Mandatory Fields:** Ensure all new `required` fields have appropriate values or defaults.
-   [ ] **Updating Validators:** Script using `db.runCommand({ collMod: "collectionName", validator: { $jsonSchema: { ...newSchema... } } })`.
-   [ ] **Creating New Indexes:** Script using `db.collection.createIndex({ field: 1 }, { unique: true })`.
-   [ ] **Removing Old Indexes:** Script using `db.collection.dropIndex("indexName")`.

1.  **Parts Collection Migration**
2.  **Warehouses Collection Migration**
3.  **Suppliers Collection Migration**
4.  **PurchaseOrders Collection Migration**
5.  **Products Collection Migration**
6.  **Categories Collection Migration**
7.  **InventoryTransactions Collection Migration**
8.  **WorkOrders Collection Migration**
9.  **Deliveries Collection Migration**
10. **QualityChecks Collection Migration**
11. **Users Collection Migration** (if schema changed)
12. **Customers Collection Migration** (if schema changed or new)

### B. Data Seeding (`seeders/`)

-   [ ] Update or create seed scripts if new reference data is required (e.g., default categories, user roles).

### C. Migration Strategy & Execution

-   [ ] **Backup:** Ensure a full database backup is taken before starting migration.
-   [ ] **Staging Environment:** Test migration scripts thoroughly in a staging environment.
-   [ ] **Downtime:** Plan for potential downtime or a maintenance window if necessary.
-   [ ] **Execution Order:** Define the correct order for running migration scripts, especially if there are dependencies.
-   [ ] **Verification:** Post-migration, verify data integrity and schema compliance.

## IV. Testing

### A. Unit Tests

-   [ ] Update tests for models (validation, methods).
-   [ ] Update tests for services (business logic, data transformation).
-   [ ] Update tests for controllers/API handlers (request/response handling).

### B. Integration Tests

-   [ ] Update tests for API endpoints (payloads, responses, status codes).
-   [ ] Update tests for cross-service interactions.
-   [ ] Test data integrity after simulated migrations.

### C. End-to-End (E2E) Tests

-   [ ] Update E2E scenarios to reflect UI changes and new data flows.
-   [ ] Verify critical user workflows with the new schema.

## V. Utilities & Libraries

-   [ ] Identify and update any utility functions, shared libraries, or reporting tools that consume or produce data based on the old schema.

## VI. Documentation

-   [ ] **API Documentation:** Update Swagger/OpenAPI specifications.
-   [ ] **Internal Design Documents:** Reflect schema changes in system architecture and data model diagrams.
-   [ ] **User Guides/Manuals:** Update if UI/UX is impacted or new features are introduced due to schema changes.
-   [ ] **Developer Onboarding Docs:** Ensure new schema is documented for new team members.

## VII. Rollback Plan

-   [ ] **Strategy Definition:**
    -   [ ] Option 1: Database snapshot restore (requires downtime).
    -   [ ] Option 2: Revert migration scripts (if reversible transformations were used).
    -   [ ] Option 3: Shadow-write to old schema during transition (complex, allows zero-downtime rollback of application code).
-   [ ] **Code Reversion:** Have previous application code version ready for deployment.
-   [ ] **Data Reversion Scripts:** Prepare scripts to undo data transformations if possible (e.g., `$rename` back, `$unset` new fields).
-   [ ] **Communication Plan:** How to communicate rollback to stakeholders/users.
-   [ ] **Trigger Conditions:** Define what conditions would trigger a rollback.

## VIII. Open Questions / Assumptions

-   [ ] Assumption: A `User` collection exists or will be created for fields like `managerId`, `approvedBy`, `userId`, `inspectorId`.
-   [ ] Assumption: A `Customer` collection exists or will be created for `delivery.customerId`.
-   [ ] Question: Specific GeoJSON library/usage for `warehouse.location.coordinates`?
-   [ ] Question: Are there any existing data anomalies that need to be cleaned up before/during migration?
-   [ ] Question: Performance implications of new indexes or complex queries on large datasets?

This checklist provides a comprehensive guide. Adjust and add specific tasks based on the unique complexities of the Trend IMS application.
