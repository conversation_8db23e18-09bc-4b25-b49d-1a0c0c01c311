# Comprehensive Schema Alignment Checklist

This checklist is generated directly from `database_schema_updated.md` and covers **all collections and fields** (including embedded/nested fields) in the IMS database. Use this as the master reference for codebase alignment.

For each collection:
- **Canonical Fields**: All fields as defined in the schema (including nested fields).
- **Legacy/Incorrect Fields**: To be filled as mismatches are found in the codebase (add file/line/context as you audit).
- **Checklist**: [ ] Update all codebase references to use canonical names and structures.

---

## 1. parts
- **Canonical Fields:**
  - _id: ObjectId
  - partNumber: String
  - name: String
  - description: String
  - technicalSpecs: String | Null
  - isManufactured: Boolean
  - reorderLevel: Int32 | Null
  - status: String
  - inventory: Object
    - currentStock: Int32
    - warehouseId: ObjectId
    - safetyStockLevel: Int32
    - maximumStockLevel: Int32
    - averageDailyUsage: Double
    - abcClassification: String
    - lastStockUpdate: Date | Null
  - supplierId: ObjectId | Null
  - unitOfMeasure: String
  - costPrice: Double
  - categoryId: ObjectId | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code (models, DTOs, forms, services, API) uses these canonical names and structures.

## 2. warehouses
- **Canonical Fields:**
  - _id: ObjectId
  - location_id: String
  - name: String
  - location: String
  - capacity: Int32
  - manager: String
  - contact: String
  - isBinTracked: Boolean
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 3. suppliers
- **Canonical Fields:**
  - _id: ObjectId
  - supplier_id: String
  - name: String
  - contactPerson: String
  - email: String
  - phone: String
  - address: String
  - specialty: Array<String>
  - rating: Double | Int32
  - payment_terms: String | Null
  - delivery_terms: String | Null
  - is_active: Boolean
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 4. users
- **Canonical Fields:**
  - _id: ObjectId
  - username: String
  - email: String
  - fullName: String
  - role: String
  - passwordHash: String
  - isActive: Boolean
  - lastLoginAt: Date | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 5. categories
- **Canonical Fields:**
  - _id: ObjectId
  - name: String
  - description: String
  - parentCategory: ObjectId | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 6. products
- **Canonical Fields:**
  - _id: ObjectId
  - productCode: String
  - name: String
  - description: String
  - categoryId: ObjectId
  - status: String
  - sellingPrice: Double
  - assemblyId: ObjectId | Null
  - partId: ObjectId | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 7. assemblies
- **Canonical Fields:**
  - _id
  - assemblyCode
  - name
  - productId
  - parentId
  - isTopLevel
  - partsRequired (partId, quantityRequired, unitOfMeasure)
  - status
  - version
  - manufacturingInstructions
  - estimatedBuildTime
  - createdBy
  - images
  - attributes
  - notes
  - createdAt
  - updatedAt
- **Legacy/Incorrect Fields:**
  - assembly_id (legacy, should be replaced by assemblyCode)
  - components (legacy, should be replaced by partsRequired)
  - assembly_stage (legacy, should be replaced by status)
  - parts (legacy, should be replaced by partsRequired)
  - quantity (should be quantityRequired)
  - unit_of_measure (should be unitOfMeasure)
  - item_id, item_type (legacy, not in canonical schema)
  - description (sometimes omitted in API)
  - status (sometimes uses legacy values)
  - partsRequired[].quantity (should be quantityRequired)
  - partsRequired[].unit_of_measure (should be unitOfMeasure)
- **Code Locations Updated:**
  - ✅ app/components/forms/UnifiedAssemblyForm.tsx (form mapping, legacy part fields)
  - ✅ app/components/tables/AssembliesTable/types.ts (components, assembly_id, assembly_stage)
  - ✅ app/(main)/assemblies/[id]/edit/AssemblyFormContent.tsx (parts, quantity, part_id)
  - ✅ app/services/assembly.service.ts (DTOs, legacy fields)
  - ✅ app/services/mongodb.ts (addAssembly, legacy field handling)
  - ✅ scripts/check-assembly-duplicates.js (assembly_id, parts, assembly_stage)
- **Checklist:**
  - [x] Update all codebase references to use canonical names and structure for assemblies
  - [x] Remove or migrate legacy/incorrect fields
  - [x] Ensure all forms, DTOs, and API logic use canonical schema
  - [x] Verify with sample data and API responses

## 8. transactions
- **Canonical Fields:**
  - _id: ObjectId
  - partId: ObjectId
  - warehouseId: ObjectId
  - transactionType: String
  - quantity: Int32
  - previousStock: Int32
  - newStock: Int32
  - transactionDate: Date
  - referenceNumber: String | Null
  - referenceType: String | Null
  - userId: ObjectId
  - notes: String
  - createdAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 9. purchaseorders
- **Canonical Fields:**
  - _id: ObjectId
  - poNumber: String
  - supplierId: ObjectId
  - orderDate: Date
  - expectedDeliveryDate: Date
  - items: Array<Object>
    - partId: ObjectId
    - description: String
    - quantity: Int32
    - unitPrice: Double
    - lineTotal: Double
    - receivedQuantity: Int32
  - totalAmount: Double
  - status: String
  - notes: String
  - shippingAddress: String
  - billingAddress: String
  - termsAndConditions: String
  - createdBy: ObjectId
  - approvedBy: ObjectId | Null
  - approvalDate: Date | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 10. workorders
- **Canonical Fields:**
  - _id: ObjectId
  - woNumber: String
  - assemblyId: ObjectId | Null
  - partIdToManufacture: ObjectId | Null
  - productId: ObjectId | Null
  - quantity: Int32
  - status: String
  - priority: String
  - dueDate: Date
  - startDate: Date | Null
  - completedAt: Date | Null
  - assignedTo: ObjectId | Null
  - notes: String
  - sourceDemand: String | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 11. batches
- **Canonical Fields:**
  - _id: ObjectId
  - batchCode: String
  - workOrderId: ObjectId
  - partId: ObjectId | Null
  - assemblyId: ObjectId | Null
  - quantityPlanned: Int32
  - quantityProduced: Int32
  - quantityScrapped: Int32
  - startDate: Date | Null
  - endDate: Date | Null
  - status: String
  - notes: String
  - assignedMachine: String | Null
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 12. batchlogs
- **Canonical Fields:**
  - _id: ObjectId
  - batchId: ObjectId
  - timestamp: Date
  - event: String
  - userId: ObjectId
  - details: String
  - data: Object | Null
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 13. deliveries
- **Canonical Fields:**
  - _id: ObjectId
  - deliveryId: String
  - referenceType: String
  - referenceId: ObjectId
  - supplierId: ObjectId | Null
  - customerId: ObjectId | Null
  - status: String
  - scheduledDate: Date
  - actualDate: Date | Null
  - trackingNumber: String | Null
  - carrier: String | Null
  - notes: String
  - receivedBy: ObjectId | Null
  - itemsDelivered: Array<Object>
    - partId: ObjectId | Null
    - productId: ObjectId | Null
    - quantity: Int32
  - createdAt: Date
  - updatedAt: Date
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 14. settings
- **Canonical Fields:**
  - _id: ObjectId
  - key: String
  - value: String
  - description: String
  - dataType: String
  - group: String
  - lastModifiedBy: ObjectId
  - lastModifiedAt: Date
  - isSystemEditableOnly: Boolean
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

## 15. IMS_TEJ
- **Canonical Fields:**
  - _id: ObjectId
  - timestamp: Date
  - eventType: String
  - level: String
  - message: String
  - source: String
  - details: Object
  - userId: ObjectId | Null
  - correlationId: String | Null
- **Legacy/Incorrect Fields:**
  - (Add as found)
- **Checklist:**
  - [ ] Ensure all code uses these canonical names and structures.

---

**Instructions:**
- For each collection, audit the codebase for mismatches and fill in the 'Legacy/Incorrect Fields' and code locations.
- Use this as the master checklist for codebase refactoring and schema alignment.