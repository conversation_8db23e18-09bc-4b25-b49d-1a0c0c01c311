# Failing Tests Task List

## Fixed Issues

- [x] AssembliesTableClient Component Tests
  - [x] Fixed "should render table headers correctly" test - Issue with multiple "Status" column headers
  - [x] Fixed "should render table headers correctly in simple mode" test - Issue with multiple "Status" column headers 
  - [x] Fixed "should render action dropdown for each row when not in simple mode" test - Missing Actions button with correct role

- [x] WorkOrderForm Component Tests
  - [x] Fixed "renders the form with initial data" test - Addressed fetch call expectations
  - [x] Fixed "calls onSubmit when form is submitted" test - Form submission handler not being called
  - [x] Fixed "calls onCancel when cancel button is clicked" test - Cancel handler not being called

- [x] Database Tests
  - [x] Added TextEncoder/TextDecoder polyfills in jest.setup.js
  - [x] Added mongoose mock for Schema.Types.ObjectId and Schema.Types.Mixed

- [x] Status Components Tests
  - [x] Fixed "PartsCountBadge" test - Updated to work with proper part structure
  - [x] Fixed "AssemblyStatusBadge" test - Updated to use partsRequired and assemblyCode fields

- [x] Action Components Tests
  - [x] Fixed "RefreshDataButton" test - Updated to match actual component structure

- [x] EnhancedPartForm Tests
  - [x] Fixed "renders form fields correctly" test
  - [x] Fixed "calls onClose when close button is clicked" test
  - [x] Fixed "calls onSubmit with form data when submitted" test

- [x] Accessibility Tests
  - [x] Fixed all tests in useAnnounce and useKeyboardNavigation
  - [x] Fixed useFocusTrap and useFocusReturn tests
  - [x] Resolved JSX parsing errors in accessibility.ts by using React.createElement instead of JSX in .ts file

- [x] MongoDB Schema Mocking
  - [x] Added proper Schema.Types.ObjectId mock implementation
  - [x] Added Schema.Types.Mixed mock support
  - [x] Created mock for systemLog.model.ts
  - [x] Created mock for logging.ts service

- [x] NextRequest/NextResponse Mocking
  - [x] Created proper mock implementation for NextRequest
  - [x] Created proper mock implementation for NextResponse
  - [x] Set up direct mocking in jest.setup.js

- [x] UI Component Syntax Errors
  - [x] Fixed ui-enhancement-plan.ts syntax error (invalid comment format)
  - [x] Fixed Card component imports in PartSearch.tsx

## Remaining Issues

### High Priority
- [ ] API Route Tests
  - [ ] Fix paths/import issues in mock files
  - [ ] Fix request/response handling in API tests
  - [ ] Address timeout issues in API tests

- [ ] Model Tests
  - [ ] Fix remaining implementation issues with mongoose models
  - [ ] Address validation methods in model tests
  - [ ] Fix MongoDB connection/memory server issues

### Medium Priority
- [ ] Hooks Tests
  - [ ] Fix remaining implementation issues
  - [ ] Ensure mock implementations properly handle state

### Low Priority
- [ ] Card Component Import Issues
  - [ ] Fix import paths for UI components
  - [ ] Add mocks for shadcn/ui components

## Strategy

1. Focus on fixing the common infrastructure issues first (MongoDB connection, Schema.Types, etc.)
2. Implement better mocks for mongoose models and Next.js API features
3. Address component-specific issues
4. Fix UI component import errors

## Core Changes Made

1. Added TextEncoder/TextDecoder polyfills to jest.setup.js
2. Created proper mock implementations for mongoose models
3. Fixed test expectations for component interactions
4. Improved form testing pattern to properly test submit handlers
5. Added environment variable mocks for MongoDB connections
6. Implemented proper mocks for NextRequest and NextResponse
7. Created dedicated mocks for part.model.ts and systemLog.model.ts
8. Fixed accessibility hooks tests with proper mocking
9. Addressed JSX syntax errors in accessibility.ts and ui-enhancement-plan.ts
10. Fixed Card component imports in search components

## Progress Summary

- Starting point: 74 failing test suites, 23 failing tests
- Current progress: 
  - Fixed several component test suites completely (EnhancedPartForm, WorkOrderForm, Accessibility Hooks)
  - Fixed major infrastructure issues (mongoose mocks, polyfills, Next.js API mocks)
  - Fixed critical syntax errors in UI components
  - Implemented MCP Postman server for API testing
- Next focus: Fix model tests and API routes by implementing proper mocks for MongoDB connections and Next.js API route handlers

The remaining issues are mostly related to mocking complex dependencies rather than actual bugs in the application code. The strategy is to create comprehensive mocks for MongoDB, Next.js API routes, and UI components to enable proper testing of the application functionality.

## Next Steps

1. Extend MongoDB mocking for API route tests
2. Use MCP Postman server for API route testing
3. Address other component test failures
4. Run complete test suite to catch any other issues

## API Test Strategy with MCP Postman

We've implemented the MCP Postman server to help with API testing. This approach allows us to:

1. Create Postman collections for each API route
2. Define tests within the collections for expected responses
3. Run the collections using Newman through the MCP server
4. Integrate these tests into our Jest test suite

For more details on the implementation, see the documentation in `docs/mcp-postman-usage.md`.

## Known Issues

There are still several error patterns appearing in the test coverage output:

1. ~~Failed to collect coverage from app/config/ui-enhancement-plan.ts (Unterminated regexp literal)~~ - Fixed
2. ~~Failed to collect coverage from app/hooks/accessibility.ts (Expected '>', got 'aria')~~ - Fixed
3. ~~Failed to collect coverage from app/components/search/PartSearch.tsx (Unexpected token 'Card')~~ - Fixed

## Current Issues

### Test Timeouts
Many tests are timing out with a 5000ms timeout error:

- [ ] Fix test timeouts in `__tests__/api/parts.integration.test.ts`
- [ ] Fix test timeouts in `__tests__/api/inventory/route.test.ts`
- [ ] Fix test timeouts in `__tests__/api/products/route.test.ts`
- [ ] Fix test timeouts in `__tests__/api/inventory-transactions/route.test.ts`
- [ ] Fix test timeouts in `__tests__/api/parts/route.test.ts`

### Other Test Failures

- [ ] Fix `__tests__/api/parts/[id]/route.test.ts` - API response status code issues (expected 404, got 500)
- [ ] Fix `__tests__/api/parts/[id]/route.test.ts` - API response status code issues (expected 400, got 500)
- [ ] Fix missing assertions in multiple API tests

## API Test Issues

- [ ] Fix Request is not defined error in API tests:
  - [ ] inventory/route.test.ts
  - [ ] hierarchical-part-entry/route.test.ts
  - [ ] parts/route.test.ts
  - [ ] batches/logs.test.ts
  - [ ] hierarchical-builder/route.test.ts
  - [ ] batches/inventory.test.ts
  - [ ] inventory-transactions/route.test.ts
  - [ ] categories/route.test.ts
  - [ ] batch-tracking/route.test.ts
  - [ ] assemblies/route.test.ts
  - [ ] analytics/route.test.ts
  - [ ] error-handling.test.ts

## Additional Model Tests Issues

- [ ] Fix MongoDB connection issues for all model tests:
  - [ ] transaction.model.test.ts
  - [ ] warehouse.model.test.ts
  - [ ] workOrder.model.test.ts
  - [ ] purchaseOrder.model.test.ts
  - [ ] settings.model.test.ts
  - [ ] batch.model.test.ts
  - [ ] supplier.model.test.ts
  - [ ] part.model.test.ts
  - [ ] assembly.model.test.ts
  - [ ] delivery.model.test.ts
  - [ ] systemLog.model.test.ts
  - [ ] category.model.test.ts
  - [ ] batchLog.model.test.ts

## Integration Tests

- [ ] Fix integration test issues:
  - [ ] parts.integration.test.ts
  - [ ] mongodb.integration.test.ts

## Additional Issues

- [ ] Review and fix remaining 67 failing test suites
- [ ] Ensure all 23 failing tests are properly addressed 