# MongoDB Schema Review: Inventory Items

This document outlines the review process for the current MongoDB schema related to inventory items in the Trend_IMS system.

## 1. Identify Collections Related to Inventory

*   **Action:** List all MongoDB collections that store or reference inventory data.
*   **Collections (Assumed/Example):**
    *   `inventory_items`
    *   `product_categories`
    *   `suppliers`
    *   `stock_movements`
    *   `price_history`

## 2. Document Fields, Data Types, and Purpose (for each collection)

### 2.1. `inventory_items` Collection

*   **Field:** `_id`
    *   **Data Type:** ObjectId
    *   **Purpose:** Unique identifier for the inventory item.
*   **Field:** `sku`
    *   **Data Type:** String
    *   **Purpose:** Stock Keeping Unit, unique product identifier.
    *   **Notes:** Indexed for fast lookups.
*   **Field:** `name`
    *   **Data Type:** String
    *   **Purpose:** Product name.
*   **Field:** `description`
    *   **Data Type:** String
    *   **Purpose:** Detailed product description.
*   **Field:** `categoryId`
    *   **Data Type:** ObjectId
    *   **Purpose:** Reference to the `product_categories` collection.
    *   **Notes:** Consider embedding category name for frequent display needs vs. normalization.
*   **Field:** `supplierId`
    *   **Data Type:** ObjectId
    *   **Purpose:** Reference to the `suppliers` collection.
*   **Field:** `quantity`
    *   **Data Type:** Number (Integer)
    *   **Purpose:** Current stock level.
*   **Field:** `reorderLevel`
    *   **Data Type:** Number (Integer)
    *   **Purpose:** Threshold for reordering stock.
*   **Field:** `costPrice`
    *   **Data Type:** Number (Decimal/Double)
    *   **Purpose:** Cost of acquiring the item.
*   **Field:** `sellingPrice`
    *   **Data Type:** Number (Decimal/Double)
    *   **Purpose:** Price at which the item is sold.
*   **Field:** `attributes`
    *   **Data Type:** Array of Objects (e.g., `{key: "color", value: "red"}`)
    *   **Purpose:** Custom product attributes.
    *   **Notes:** Flexible schema, but querying specific attributes might require careful indexing.
*   **Field:** `images`
    *   **Data Type:** Array of Strings (URLs)
    *   **Purpose:** URLs of product images.
*   **Field:** `createdAt`
    *   **Data Type:** Date
    *   **Purpose:** Timestamp of item creation.
*   **Field:** `updatedAt`
    *   **Data Type:** Date
    *   **Purpose:** Timestamp of last update.
*   **Field:** `isActive`
    *   **Data Type:** Boolean
    *   **Purpose:** Indicates if the product is currently active/listed.

*(Repeat for other identified collections like `product_categories`, `suppliers`, etc.)*

## 3. Analyze Indexing Strategies

*   **Action:** Review existing indexes on all relevant collections.
*   **`inventory_items` Indexes (Example):**
    *   `_id` (default)
    *   `sku: 1` (unique)
    *   `categoryId: 1`
    *   `name: "text", description: "text"` (for text search)
    *   `quantity: 1` (for low stock queries)
*   **Considerations:**
    *   Are there queries that are slow due to missing indexes?
    *   Are there unused indexes that can be removed?
    *   Compound indexes for common query patterns.
    *   Partial indexes for specific subsets of data.

## 4. Identify Potential Normalization/Denormalization Opportunities

*   **Action:** Evaluate trade-offs between data redundancy (denormalization for read performance) and data consistency (normalization to avoid update anomalies).
*   **Examples:**
    *   **Denormalization:** Embedding `categoryName` in `inventory_items` if frequently displayed with items and category names rarely change.
    *   **Normalization:** Keeping `supplier` details in a separate collection and referencing by `supplierId` to avoid data duplication if supplier info is extensive and updates often.
*   **Decision Criteria:** Read/write patterns, data consistency requirements, complexity of updates.

## 5. Check for Data Consistency and Integrity Issues

*   **Action:** Look for orphaned records, inconsistent data types, or validation rule violations.
*   **Examples:**
    *   `inventory_items` with `categoryId` that doesn't exist in `product_categories`.
    *   Negative `quantity` values where not allowed.
    *   Inconsistent date formats (if not using ISODate).
*   **Tools/Methods:** Write aggregation queries to find inconsistencies.

## 6. Document Findings and Recommendations

### Findings

*   *(Example) Finding 1:* The `attributes` field in `inventory_items` is an array of key-value pairs, which offers flexibility but makes querying specific attribute values less efficient without targeted indexing strategies (e.g., on `attributes.key` and `attributes.value`).
*   *(Example) Finding 2:* `product_categories` are referenced by `categoryId`, but the category name is frequently needed in inventory listings, leading to frequent lookups/joins.
*   *(Example) Finding 3:* No clear indexing strategy for searching items by multiple `attributes` simultaneously.

### Recommendations

*   *(Example) Recommendation 1:* For the `attributes` field, consider creating a compound index on `{"attributes.key": 1, "attributes.value": 1}` if specific key-value searches are common. Alternatively, for very common attributes, consider promoting them to top-level fields.
*   *(Example) Recommendation 2:* Denormalize `categoryName` into the `inventory_items` collection to improve read performance for listings. Implement a mechanism to update `categoryName` in `inventory_items` if the name changes in `product_categories`.
*   *(Example) Recommendation 3:* Evaluate the most common attribute-based search patterns and create appropriate compound indexes or explore schema redesign for these attributes if performance is critical.
*   *(Example) Recommendation 4:* Implement schema validation at the database level (if MongoDB version supports it well) or application level to enforce data types and required fields for `inventory_items`.

## Next Steps

*   Discuss findings with the development team.
*   Prioritize recommended schema changes.
*   Plan and implement approved changes with appropriate data migration strategies.