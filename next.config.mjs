// Import bundle analyzer with fallback
// import bundleAnalyzer from '@next/bundle-analyzer';

// const withBundleAnalyzer = bundleAnalyzer;

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  reactStrictMode: true,

  // PERFORMANCE OPTIMIZATION: Enable compression and optimize responses
  compress: true,

  // DEPLOYMENT CONFIGURATION: Support for flexible port configuration
  // Enable standalone output for production deployments
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,

  // Global headers for API routes to ensure JSON responses
  async headers() {
    return [
      {
        // Apply to all API routes
        source: '/api/:path*',
        headers: [
          { key: 'Content-Type', value: 'application/json' },
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization, Cache-Control, Pragma, Expires' },
        ],
      },
    ];
  },

  // Environment variables that should be available at build time
  env: {
    // These will be replaced at build time
    CUSTOM_PORT: process.env.PORT || (process.env.NODE_ENV === 'production' ? '3000' : '5174'),
  },

  // Optimize images
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // Add other configurations here as needed
  // e.g., experimental features, redirects, rewrites, etc.

  // Exclude specific directories from build
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],
  webpack: (config, { isServer, dev }) => {
    // Exclude src, src_old, backup, and src_older_backup directories
    config.module.rules.push({
      test: /\.(js|jsx|ts|tsx)$/,
      exclude: /src|backup|src_old|src_older_backup/,
      use: []
    });

    // Fix for MongoDB Node.js modules in client components
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        'fs/promises': false,
        'timers/promises': false,
      };
      
      // Add custom handling for HMR in development to fix "Unrecognized HMR message" errors
      if (dev) {
        // Suppress the HMR ping error
        const DefinePlugin = config.plugins.find(
          (plugin) => plugin.constructor.name === 'DefinePlugin'
        );
        
        if (DefinePlugin) {
          // Add a global flag to silence the HMR ping error
          DefinePlugin.definitions['process.env.NEXT_SUPPRESS_HMR_ERROR'] = JSON.stringify(true);
        }
        
        // Modify the hot middleware client to ignore the ping messages
        const originalEntry = config.entry;
        config.entry = async () => {
          const entries = await (typeof originalEntry === 'function' ? originalEntry() : originalEntry);
          
          // Find and modify the client-side entry points
          Object.keys(entries).forEach((entry) => {
            if (entries[entry] && Array.isArray(entries[entry])) {
              // Look for the webpack-hot-middleware client entry
              const hmrIndex = entries[entry].findIndex((file) => 
                file.includes('webpack-hot-middleware')
              );
              
              if (hmrIndex !== -1) {
                console.log(`[HMR Fix] Applying custom HMR client config for entry: ${entry}`);
                // Replace with our customized HMR client that ignores ping messages
                entries[entry][hmrIndex] = entries[entry][hmrIndex] + '?quiet=true&overlay=false';
              }
            }
          });
          
          return entries;
        };
      }
    }

    return config;
  },

  // Optimize HMR in development mode
  experimental: {
    // Improve HMR reliability
    optimizeServerReact: true,
    // Disable automatic static optimization for more reliable HMR
    disableOptimizedLoading: (typeof process !== 'undefined' && process.env.NODE_ENV === 'development'),
    // Moved trace config to avoid warnings
  },

  // Next.js 15+ requires different Sentry configuration
  // These options will be passed to the Sentry plugin
  // Enable Sentry integration is now handled by the withSentryConfig wrapper
};

// Enable bundle analyzer in development when ANALYZE is set
// const analyzeBundleEnabled = typeof process !== 'undefined' && process.env.ANALYZE === 'true';
// const bundleAnalyzerWrapper = withBundleAnalyzer({
//   enabled: analyzeBundleEnabled,
//   openAnalyzer: false, // Don't auto-open in CI/production
// });

// Export configuration without bundle analyzer for now
export default nextConfig;

// To enable Sentry, replace the above line with:
// export default shouldEnableSentry
//   ? bundleAnalyzer(withSentryConfig(nextConfig, sentryConfig))
//   : bundleAnalyzer(nextConfig);